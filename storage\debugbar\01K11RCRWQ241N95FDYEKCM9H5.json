{"__meta": {"id": "01K11RCRWQ241N95FDYEKCM9H5", "datetime": "2025-07-25 21:38:57", "utime": **********.560941, "method": "POST", "uri": "/en/account/properties/create", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[21:38:57] LOG.error: Method Illuminate\\Validation\\Validator::validateAny does not exist. {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.069284, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.351421, "end": **********.560962, "duration": 1.***************, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": **********.351421, "relative_start": 0, "end": **********.988136, "relative_end": **********.988136, "duration": 0.****************, "duration_str": "637ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.988147, "relative_start": 0.***************, "end": **********.560964, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "573ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.00436, "relative_start": 0.****************, "end": **********.021341, "relative_end": **********.021341, "duration": 0.016981124877929688, "duration_str": "16.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "BadMethodCallException", "message": "Method Illuminate\\Validation\\Validator::validateAny does not exist.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "line": 1646, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:88</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>660</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">validateAny</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"115 characters\">accounts/m-233/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => []\n        <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\Validator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Validator</span></span> {<a class=sf-dump-ref>#3584</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">translator</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\Translator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Translator</span></span> {<a class=sf-dump-ref>#1003</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">parsed</span>: <span class=sf-dump-note>array:40</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_login_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin_login_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_forgot_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"26 characters\">admin_forgot_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha::captcha.admin_reset_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/captcha</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">captcha</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"25 characters\">admin_reset_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_year</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">current_year</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_month</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">current_month</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug::slug.current_day</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/slug</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">current_day</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.login_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">login_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.register_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">register_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.forgot_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"20 characters\">forgot_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.reset_password_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"19 characters\">reset_password_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.consult_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">consult_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.review_form</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">review_form</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/menu::menu.main_navigation</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/menu</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">menu</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">main_navigation</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/page::pages.pages</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">packages/page</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">pages</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::property.properties</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">property</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">properties</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::category.property_categories</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"19 characters\">property_categories</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::project.projects</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">project</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">projects</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::account.agents</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">account</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">agents</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.primary_sidebar_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"20 characters\">primary_sidebar_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.primary_sidebar_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"27 characters\">primary_sidebar_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_menu</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">widget_menu</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_menu_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">widget_menu_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_text</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">widget_text</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget::widget.widget_text_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">packages/widget</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">widget</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">widget_text_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact::contact.shortcode_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/contact</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">contact</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"14 characters\">shortcode_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact::contact.shortcode_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">plugins/contact</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">contact</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"21 characters\">shortcode_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.slug_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"23 characters\">theme_options.slug_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.slug_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"30 characters\">theme_options.slug_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.page_slugs.projects_city</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"38 characters\">theme_options.page_slugs.projects_city</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.page_slug_name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"28 characters\">theme_options.page_slug_name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.page_slug_description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"35 characters\">theme_options.page_slug_description</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.page_slugs.projects_state</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"39 characters\">theme_options.page_slugs.projects_state</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::real-estate.theme_options.page_slugs.projects_country</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">real-estate</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"41 characters\">theme_options.page_slugs.projects_country</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>Popular Cities</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">Popular Cities</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>Paopular Cities</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"15 characters\">Paopular Cities</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>Hero Section</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Hero Section</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>New Properties</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">New Properties</span>\"\n                <span class=sf-dump-index>2</span> => <span class=sf-dump-const>null</span>\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::property.distance_key</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">property</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">distance_key</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::custom-fields.name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">custom-fields</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate::property.floor_plans.title</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">plugins/real-estate</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">property</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">floor_plans.title</span>\"\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">loader</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Translation\\FileLoader\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Translation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FileLoader</span></span> {<a class=sf-dump-ref>#1004</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Filesystem</span></span> {<a class=sf-dump-ref>#209</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">paths</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"77 characters\">D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Translation/lang</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"25 characters\">D:\\laragon\\www\\xmetr\\lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">jsonPaths</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr/lang</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"45 characters\">D:\\laragon\\www\\xmetr\\lang\\vendor/themes/xmetr</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">hints</span>: <span class=sf-dump-note>array:36</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>packages/api</span>\" => \"<span class=sf-dump-str title=\"77 characters\">D:\\laragon\\www\\xmetr\\platform\\packages\\api\\src\\Providers/../../resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/data-synchronize</span>\" => \"<span class=sf-dump-str title=\"90 characters\">D:\\laragon\\www\\xmetr\\platform\\packages\\data-synchronize\\src\\Providers/../../resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/get-started</span>\" => \"<span class=sf-dump-str title=\"65 characters\">D:\\laragon\\www\\xmetr\\platform/packages/get-started/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/installer</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\xmetr\\platform/packages/installer/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/menu</span>\" => \"<span class=sf-dump-str title=\"58 characters\">D:\\laragon\\www\\xmetr\\platform/packages/menu/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/optimize</span>\" => \"<span class=sf-dump-str title=\"62 characters\">D:\\laragon\\www\\xmetr\\platform/packages/optimize/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/page</span>\" => \"<span class=sf-dump-str title=\"58 characters\">D:\\laragon\\www\\xmetr\\platform/packages/page/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/setting</span>\" => \"<span class=sf-dump-str title=\"57 characters\">D:\\laragon\\www\\xmetr\\platform/core/setting/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/base</span>\" => \"<span class=sf-dump-str title=\"54 characters\">D:\\laragon\\www\\xmetr\\platform/core/base/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/table</span>\" => \"<span class=sf-dump-str title=\"55 characters\">D:\\laragon\\www\\xmetr\\platform/core/table/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/acl</span>\" => \"<span class=sf-dump-str title=\"53 characters\">D:\\laragon\\www\\xmetr\\platform/core/acl/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/dashboard</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\xmetr\\platform/core/dashboard/resources/lang</span>\"\n                \"<span class=sf-dump-key>core/media</span>\" => \"<span class=sf-dump-str title=\"55 characters\">D:\\laragon\\www\\xmetr\\platform/core/media/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/plugin-management</span>\" => \"<span class=sf-dump-str title=\"71 characters\">D:\\laragon\\www\\xmetr\\platform/packages/plugin-management/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/seo-helper</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\xmetr\\platform/packages/seo-helper/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/shortcode</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\xmetr\\platform/packages/shortcode/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/slug</span>\" => \"<span class=sf-dump-str title=\"58 characters\">D:\\laragon\\www\\xmetr\\platform/packages/slug/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/theme</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\xmetr\\platform/packages/theme/resources/lang</span>\"\n                \"<span class=sf-dump-key>packages/widget</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\xmetr\\platform/packages/widget/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/language</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/language/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/ads</span>\" => \"<span class=sf-dump-str title=\"56 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/ads/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/analytics</span>\" => \"<span class=sf-dump-str title=\"62 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/analytics/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/audit-log</span>\" => \"<span class=sf-dump-str title=\"62 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/audit-log/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/backup</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/backup/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/captcha</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/captcha/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/contact</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/contact/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/cookie-consent</span>\" => \"<span class=sf-dump-str title=\"67 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/cookie-consent/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/faq</span>\" => \"<span class=sf-dump-str title=\"56 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/faq/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/location</span>\" => \"<span class=sf-dump-str title=\"61 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/location/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/newsletter</span>\" => \"<span class=sf-dump-str title=\"63 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/newsletter/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/payment</span>\" => \"<span class=sf-dump-str title=\"60 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/payment/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/real-estate</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/social-login</span>\" => \"<span class=sf-dump-str title=\"65 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/social-login/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/stripe</span>\" => \"<span class=sf-dump-str title=\"59 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/stripe/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/testimonial</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/testimonial/resources/lang</span>\"\n                \"<span class=sf-dump-key>plugins/translation</span>\" => \"<span class=sf-dump-str title=\"64 characters\">D:\\laragon\\www\\xmetr\\platform/plugins/translation/resources/lang</span>\"\n              </samp>]\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">locale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">fallback</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">loaded</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>*</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:821</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>-- None --</span>\" => \"<span class=sf-dump-str title=\"10 characters\">-- None --</span>\"\n                    \"<span class=sf-dump-key>-- Select --</span>\" => \"<span class=sf-dump-str title=\"13 characters\">-- &#1042;&#1099;&#1073;&#1088;&#1072;&#1090;&#1100; --</span>\"\n                    \"<span class=sf-dump-key>1 Bathroom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 &#1042;&#1072;&#1085;&#1085;&#1072;&#1103;</span>\"\n                    \"<span class=sf-dump-key>1 Bedroom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1 &#1057;&#1087;&#1072;&#1083;&#1100;&#1085;&#1103;</span>\"\n                    \"<span class=sf-dump-key>1 Block</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 Block</span>\"\n                    \"<span class=sf-dump-key>1 Floor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 Floor</span>\"\n                    \"<span class=sf-dump-key>1 Property</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1 Property</span>\"\n                    \"<span class=sf-dump-key>1 Review</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 &#1054;&#1090;&#1079;&#1099;&#1074;</span>\"\n                    \"<span class=sf-dump-key>1 Views</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1 &#1087;&#1088;&#1086;&#1089;&#1084;&#1086;&#1090;&#1088;</span>\"\n                    \"<span class=sf-dump-key>1 bathroom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 &#1074;&#1072;&#1085;&#1085;&#1072;&#1103;</span>\"\n                    \"<span class=sf-dump-key>1 bedroom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1 &#1089;&#1087;&#1072;&#1083;&#1100;&#1085;&#1103;</span>\"\n                    \"<span class=sf-dump-key>1 credit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 credit</span>\"\n                    \"<span class=sf-dump-key>1 post</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1 post</span>\"\n                    \"<span class=sf-dump-key>404 - Page Not found</span>\" => \"<span class=sf-dump-str title=\"20 characters\">404 - Page Not found</span>\"\n                    \"<span class=sf-dump-key>404 Page Not Found</span>\" => \"<span class=sf-dump-str title=\"18 characters\">404 Page Not Found</span>\"\n                    \"<span class=sf-dump-key>5+ blocks</span>\" => \"<span class=sf-dump-str title=\"9 characters\">5+ blocks</span>\"\n                    \"<span class=sf-dump-key>5+ floors</span>\" => \"<span class=sf-dump-str title=\"10 characters\">5+ &#1091;&#1088;&#1086;&#1074;&#1085;&#1077;&#1081;</span>\"\n                    \"<span class=sf-dump-key>5+ rooms</span>\" => \"<span class=sf-dump-str title=\"9 characters\">5+ &#1082;&#1086;&#1084;&#1085;&#1072;&#1090;</span>\"\n                    \"<span class=sf-dump-key>500 Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"25 characters\">500 Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>503 Service Unavailable</span>\" => \"<span class=sf-dump-str title=\"23 characters\">503 Service Unavailable</span>\"\n                    \"<span class=sf-dump-key>:avg out of 5</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:avg out of 5</span>\"\n                    \"<span class=sf-dump-key>:count Properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:count Properties</span>\"\n                    \"<span class=sf-dump-key>:count Review(s)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:count Review(s)</span>\"\n                    \"<span class=sf-dump-key>:count bathrooms</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:count bathrooms</span>\"\n                    \"<span class=sf-dump-key>:count bedrooms</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count bedrooms</span>\"\n                    \"<span class=sf-dump-key>:count credits</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:count credits</span>\"\n                    \"<span class=sf-dump-key>:count photo</span>\" => \"<span class=sf-dump-str title=\"11 characters\">:count &#1092;&#1086;&#1090;&#1086;</span>\"\n                    \"<span class=sf-dump-key>:count properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:count properties</span>\"\n                    \"<span class=sf-dump-key>:count property</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count property</span>\"\n                    \"<span class=sf-dump-key>:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\"\n                    \"<span class=sf-dump-key>:name feed</span>\" => \"<span class=sf-dump-str title=\"10 characters\">:name feed</span>\"\n                    \"<span class=sf-dump-key>:name font family</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:name font family</span>\"\n                    \"<span class=sf-dump-key>:name font size</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:name font size</span>\"\n                    \"<span class=sf-dump-key>:number Bathrooms</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number &#1074;&#1072;&#1085;&#1085;&#1099;&#1093;</span>\"\n                    \"<span class=sf-dump-key>:number Bedrooms</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number &#1089;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;</span>\"\n                    \"<span class=sf-dump-key>:number Blocks</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Blocks</span>\"\n                    \"<span class=sf-dump-key>:number Column</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Column</span>\"\n                    \"<span class=sf-dump-key>:number Columns</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number Columns</span>\"\n                    \"<span class=sf-dump-key>:number Floors</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Floors</span>\"\n                    \"<span class=sf-dump-key>:number Reviews</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number Reviews</span>\"\n                    \"<span class=sf-dump-key>:number Views</span>\" => \"<span class=sf-dump-str title=\"18 characters\">:number &#1087;&#1088;&#1086;&#1089;&#1084;&#1086;&#1090;&#1088;&#1086;&#1074;</span>\"\n                    \"<span class=sf-dump-key>:number posts</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:number posts</span>\"\n                    \"<span class=sf-dump-key>:number+ Bathrooms</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number+ &#1074;&#1072;&#1085;&#1085;&#1099;&#1093;</span>\"\n                    \"<span class=sf-dump-key>:number+ Bedrooms</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:number+ &#1089;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;</span>\"\n                    \"<span class=sf-dump-key>:number+ Blocks</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number+ Blocks</span>\"\n                    \"<span class=sf-dump-key>:number+ Floors</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number+ Floors</span>\"\n                    \"<span class=sf-dump-key>:price / per post</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:price / per post</span>\"\n                    \"<span class=sf-dump-key>:price Total :percentage_sale</span>\" => \"<span class=sf-dump-str title=\"29 characters\">:price Total :percentage_sale</span>\"\n                    \"<span class=sf-dump-key>A content tab to display information with a title.</span>\" => \"<span class=sf-dump-str title=\"50 characters\">A content tab to display information with a title.</span>\"\n                    \"<span class=sf-dump-key>A new version (:version / released on :date) is available to update!</span>\" => \"<span class=sf-dump-str title=\"68 characters\">A new version (:version / released on :date) is available to update!</span>\"\n                    \"<span class=sf-dump-key>A-Z</span>\" => \"<span class=sf-dump-str title=\"3 characters\">A-Z</span>\"\n                    \"<span class=sf-dump-key>API Key</span>\" => \"<span class=sf-dump-str title=\"7 characters\">API Key</span>\"\n                    \"<span class=sf-dump-key>About</span>\" => \"<span class=sf-dump-str title=\"5 characters\">About</span>\"\n                    \"<span class=sf-dump-key>About Agent</span>\" => \"<span class=sf-dump-str title=\"11 characters\">About Agent</span>\"\n                    \"<span class=sf-dump-key>About Us</span>\" => \"<span class=sf-dump-str title=\"8 characters\">About Us</span>\"\n                    \"<span class=sf-dump-key>Accept and install</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Accept and install</span>\"\n                    \"<span class=sf-dump-key>Action</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Action</span>\"\n                    \"<span class=sf-dump-key>Active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1040;&#1082;&#1090;&#1080;&#1074;&#1085;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Add</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1044;&#1086;&#1073;&#1072;&#1074;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Add Google Maps iframe</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Add Google Maps iframe</span>\"\n                    \"<span class=sf-dump-key>Add YouTube video</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Add YouTube video</span>\"\n                    \"<span class=sf-dump-key>Add a custom menu to your widget area.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Add a custom menu to your widget area.</span>\"\n                    \"<span class=sf-dump-key>Add credit to account</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Add credit to account</span>\"\n                    \"<span class=sf-dump-key>Add custom HTML content</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Add custom HTML content</span>\"\n                    \"<span class=sf-dump-key>Add listing</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1044;&#1086;&#1073;&#1072;&#1074;&#1080;&#1090;&#1100; &#1086;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Add new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Add new</span>\"\n                    \"<span class=sf-dump-key>Add widgets here to appear in the sidebar of your blog pages.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Add widgets here to appear in the sidebar of your blog pages.</span>\"\n                    \"<span class=sf-dump-key>Added &quot;:name&quot; to wishlist successfully!</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Added &quot;:name&quot; to wishlist successfully!</span>\"\n                    \"<span class=sf-dump-key>Added :credits credit(s) by admin &quot;:user&quot;</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Added :credits credit(s) by admin &quot;:user&quot;</span>\"\n                    \"<span class=sf-dump-key>Address</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1040;&#1076;&#1088;&#1077;&#1089;</span>\"\n                    \"<span class=sf-dump-key>Ads</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Ads</span>\"\n                    \"<span class=sf-dump-key>Advanced</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Advanced</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have API key</span>\" => \"<span class=sf-dump-str title=\"50 characters\">After registration at :name, you will have API key</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Client ID, Client Secret</span>\" => \"<span class=sf-dump-str title=\"67 characters\">After registration at :name, you will have Client ID, Client Secret</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Public &amp; Secret keys</span>\" => \"<span class=sf-dump-str title=\"63 characters\">After registration at :name, you will have Public &amp; Secret keys</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\" => \"<span class=sf-dump-str title=\"87 characters\">After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\"\n                    \"<span class=sf-dump-key>Agent</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1056;&#1080;&#1101;&#1083;&#1090;&#1086;&#1088;</span>\"\n                    \"<span class=sf-dump-key>Agents</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1040;&#1075;&#1077;&#1085;&#1090;&#1099;</span>\"\n                    \"<span class=sf-dump-key>All</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1042;&#1089;&#1077;</span>\"\n                    \"<span class=sf-dump-key>All Countries</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1042;&#1089;&#1077; &#1089;&#1090;&#1088;&#1072;&#1085;&#1099;</span>\"\n                    \"<span class=sf-dump-key>All Pages</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1042;&#1089;&#1077; &#1089;&#1090;&#1088;&#1072;&#1085;&#1080;&#1094;&#1099;</span>\"\n                    \"<span class=sf-dump-key>All flats</span>\" => \"<span class=sf-dump-str title=\"9 characters\">All flats</span>\"\n                    \"<span class=sf-dump-key>All squares</span>\" => \"<span class=sf-dump-str title=\"11 characters\">All squares</span>\"\n                    \"<span class=sf-dump-key>Already have an account?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1059;&#1078;&#1077; &#1077;&#1089;&#1090;&#1100; &#1072;&#1082;&#1082;&#1072;&#1091;&#1085;&#1090;?</span>\"\n                    \"<span class=sf-dump-key>Amenities</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1059;&#1076;&#1086;&#1073;&#1089;&#1090;&#1074;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Amenities and features</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Amenities and features</span>\"\n                    \"<span class=sf-dump-key>Amenities:</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1059;&#1076;&#1086;&#1073;&#1089;&#1090;&#1074;&#1072;:</span>\"\n                    \"<span class=sf-dump-key>An error occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"39 characters\">An error occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Animation text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Animation text</span>\"\n                    \"<span class=sf-dump-key>Applied coupon &quot;:code&quot; successfully!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Applied coupon &quot;:code&quot; successfully!</span>\"\n                    \"<span class=sf-dump-key>Apply Now</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1088;&#1080;&#1084;&#1077;&#1085;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to renew this property</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Are you sure you want to renew this property</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to renew this property, it will takes 1 credit from your credits</span>\" => \"<span class=sf-dump-str title=\"86 characters\">Are you sure you want to renew this property, it will takes 1 credit from your credits</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\"\n                    \"<span class=sf-dump-key>Are you sure?</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Are you sure?</span>\"\n                    \"<span class=sf-dump-key>Area</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1055;&#1083;&#1086;&#1097;&#1072;&#1076;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Area &#1084;&#178;</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1055;&#1083;&#1086;&#1097;&#1072;&#1076;&#1100; &#1084;&#178;</span>\"\n                    \"<span class=sf-dump-key>Audio File</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Audio File</span>\"\n                    \"<span class=sf-dump-key>Author</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Author</span>\"\n                    \"<span class=sf-dump-key>Autoplay speed (if autoplay enabled)</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Autoplay speed (if autoplay enabled)</span>\"\n                    \"<span class=sf-dump-key>Back To Home</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1042;&#1077;&#1088;&#1085;&#1091;&#1090;&#1100;&#1089;&#1103; &#1085;&#1072; &#1075;&#1083;&#1072;&#1074;&#1085;&#1091;&#1102;</span>\"\n                    \"<span class=sf-dump-key>Back to login page</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Back to login page</span>\"\n                    \"<span class=sf-dump-key>Background Image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background Image</span>\"\n                    \"<span class=sf-dump-key>Background color</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background color</span>\"\n                    \"<span class=sf-dump-key>Background image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background image</span>\"\n                    \"<span class=sf-dump-key>Banner</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Banner</span>\"\n                    \"<span class=sf-dump-key>Bathrooms</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1042;&#1072;&#1085;&#1085;&#1099;&#1093;</span>\"\n                    \"<span class=sf-dump-key>Bathrooms:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1042;&#1072;&#1085;&#1085;&#1099;&#1093;:</span>\"\n                    \"<span class=sf-dump-key>Bedrooms</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1057;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Bedrooms:</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1057;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;:</span>\"\n                    \"<span class=sf-dump-key>Bill payment user account information, ex: account.name, account.email, ...</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Bill payment user account information, ex: account.name, account.email, ...</span>\"\n                    \"<span class=sf-dump-key>Blocks</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Blocks</span>\"\n                    \"<span class=sf-dump-key>Blocks:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Blocks:</span>\"\n                    \"<span class=sf-dump-key>Blog Categories</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Blog Categories</span>\"\n                    \"<span class=sf-dump-key>Blog List (after)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Blog List (after)</span>\"\n                    \"<span class=sf-dump-key>Blog List (before)</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Blog List (before)</span>\"\n                    \"<span class=sf-dump-key>Blog Posts</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Blog Posts</span>\"\n                    \"<span class=sf-dump-key>Blog Search</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Blog Search</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Blog Sidebar</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar (after)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Blog Sidebar (after)</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar (before)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Blog Sidebar (before)</span>\"\n                    \"<span class=sf-dump-key>Body</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Body</span>\"\n                    \"<span class=sf-dump-key>Bottom Footer Sidebar</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Bottom Footer Sidebar</span>\"\n                    \"<span class=sf-dump-key>Bottom Post Detail Sidebar</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Bottom Post Detail Sidebar</span>\"\n                    \"<span class=sf-dump-key>Bottom footer section for legal notices and credits.</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Bottom footer section for legal notices and credits.</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Breadcrumb</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb background color</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Breadcrumb background color</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb background image</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Breadcrumb background image</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb text color</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Breadcrumb text color</span>\"\n                    \"<span class=sf-dump-key>Built</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Built</span>\"\n                    \"<span class=sf-dump-key>Business hours</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Business hours</span>\"\n                    \"<span class=sf-dump-key>Button URL</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Button URL</span>\"\n                    \"<span class=sf-dump-key>Button label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Button label</span>\"\n                    \"<span class=sf-dump-key>Buy credits</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Buy credits</span>\"\n                    \"<span class=sf-dump-key>By clicking create an account, you accept</span>\" => \"<span class=sf-dump-str title=\"41 characters\">By clicking create an account, you accept</span>\"\n                    \"<span class=sf-dump-key>Call To Action</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Call To Action</span>\"\n                    \"<span class=sf-dump-key>Can&#039;t send message on this time, please try again later!</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Can&#039;t send message on this time, please try again later!</span>\"\n                    \"<span class=sf-dump-key>Cancel</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1054;&#1090;&#1084;&#1077;&#1085;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Cannot find this account!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Cannot find this account!</span>\"\n                    \"<span class=sf-dump-key>Cannot login, no email provided!</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Cannot login, no email provided!</span>\"\n                    \"<span class=sf-dump-key>Captcha</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Captcha</span>\"\n                    \"<span class=sf-dump-key>Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Caption</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Caption</span>\"\n                    \"<span class=sf-dump-key>Careers</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Careers</span>\"\n                    \"<span class=sf-dump-key>Categories</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"\n                    \"<span class=sf-dump-key>Category</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1050;&#1072;&#1090;&#1077;&#1075;&#1086;&#1088;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Center content</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Center content</span>\"\n                    \"<span class=sf-dump-key>Change copyright</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Change copyright</span>\"\n                    \"<span class=sf-dump-key>Check have enabled the invoice stamp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Check have enabled the invoice stamp</span>\"\n                    \"<span class=sf-dump-key>Check site is using custom font for invoice or not</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Check site is using custom font for invoice or not</span>\"\n                    \"<span class=sf-dump-key>Checklist</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Checklist</span>\"\n                    \"<span class=sf-dump-key>Checkout</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Checkout</span>\"\n                    \"<span class=sf-dump-key>Checkout error!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Checkout error!</span>\"\n                    \"<span class=sf-dump-key>Checkout successfully!</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Checkout successfully!</span>\"\n                    \"<span class=sf-dump-key>Choices</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Choices</span>\"\n                    \"<span class=sf-dump-key>Choose Country</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1042;&#1099;&#1073;&#1077;&#1088;&#1080;&#1090;&#1077; &#1089;&#1090;&#1088;&#1072;&#1085;&#1091;</span>\"\n                    \"<span class=sf-dump-key>Choose The Package</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Choose The Package</span>\"\n                    \"<span class=sf-dump-key>Choose agents</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1042;&#1099;&#1073;&#1088;&#1072;&#1090;&#1100; &#1072;&#1075;&#1077;&#1085;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Choose categories</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Choose categories</span>\"\n                    \"<span class=sf-dump-key>Choose date format for your front theme.</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Choose date format for your front theme.</span>\"\n                    \"<span class=sf-dump-key>Cities</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1043;&#1086;&#1088;&#1086;&#1076;&#1072;</span>\"\n                    \"<span class=sf-dump-key>City</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1043;&#1086;&#1088;&#1086;&#1076;</span>\"\n                    \"<span class=sf-dump-key>City, State</span>\" => \"<span class=sf-dump-str title=\"11 characters\">City, State</span>\"\n                    \"<span class=sf-dump-key>Clear</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1054;&#1095;&#1080;&#1089;&#1090;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Clear All</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1057;&#1073;&#1088;&#1086;&#1089;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Close</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1047;&#1072;&#1082;&#1088;&#1099;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Color</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1062;&#1074;&#1077;&#1090;</span>\"\n                    \"<span class=sf-dump-key>Coming Soon</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1057;&#1082;&#1086;&#1088;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Commission</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1050;&#1086;&#1084;&#1080;&#1089;&#1089;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Company Agent at :company</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Company Agent at :company</span>\"\n                    \"<span class=sf-dump-key>Contact</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Contact</span>\"\n                    \"<span class=sf-dump-key>Contact Agency</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Contact Agency</span>\"\n                    \"<span class=sf-dump-key>Contact agency</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Contact agency</span>\"\n                    \"<span class=sf-dump-key>Content</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Content</span>\"\n                    \"<span class=sf-dump-key>Content Quote</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Content Quote</span>\"\n                    \"<span class=sf-dump-key>Content Tab</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Content Tab</span>\"\n                    \"<span class=sf-dump-key>Content image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Content image</span>\"\n                    \"<span class=sf-dump-key>Continuously</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Continuously</span>\"\n                    \"<span class=sf-dump-key>Copy link</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1050;&#1086;&#1087;&#1080;&#1088;&#1086;&#1074;&#1072;&#1090;&#1100; &#1089;&#1089;&#1099;&#1083;&#1082;&#1091;</span>\"\n                    \"<span class=sf-dump-key>Copyright</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Copyright</span>\"\n                    \"<span class=sf-dump-key>Copyright on footer of site. Using %Y to display current year.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Copyright on footer of site. Using %Y to display current year.</span>\"\n                    \"<span class=sf-dump-key>Copyright text at the bottom footer.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Copyright text at the bottom footer.</span>\"\n                    \"<span class=sf-dump-key>Could not download updated file. Please check your license or your internet network.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">Could not download updated file. Please check your license or your internet network.</span>\"\n                    \"<span class=sf-dump-key>Could not update files &amp; database.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Could not update files &amp; database.</span>\"\n                    \"<span class=sf-dump-key>Countdown time</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Countdown time</span>\"\n                    \"<span class=sf-dump-key>Counters</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1057;&#1090;&#1088;&#1072;&#1085;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Country</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1057;&#1090;&#1088;&#1072;&#1085;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Coupon code: :code</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Coupon code: :code</span>\"\n                    \"<span class=sf-dump-key>Create engaging call-to-action sections with customizable headings, buttons, and images.</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Create engaging call-to-action sections with customizable headings, buttons, and images.</span>\"\n                    \"<span class=sf-dump-key>Credits</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Credits</span>\"\n                    \"<span class=sf-dump-key>Credits: :count</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Credits: :count</span>\"\n                    \"<span class=sf-dump-key>Currencies</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Currencies</span>\"\n                    \"<span class=sf-dump-key>Currency</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1042;&#1072;&#1083;&#1102;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Currency &amp; Language</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1042;&#1072;&#1083;&#1102;&#1090;&#1072; &#1080; &#1103;&#1079;&#1099;&#1082;</span>\"\n                    \"<span class=sf-dump-key>Custom HTML</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom HTML</span>\"\n                    \"<span class=sf-dump-key>Custom Menu</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom Menu</span>\"\n                    \"<span class=sf-dump-key>Customer can buy product and pay directly using Visa, Credit card via :name</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Customer can buy product and pay directly using Visa, Credit card via :name</span>\"\n                    \"<span class=sf-dump-key>Date format</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Date format</span>\"\n                    \"<span class=sf-dump-key>Days</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1044;&#1085;&#1077;&#1081;</span>\"\n                    \"<span class=sf-dump-key>Default</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n                    \"<span class=sf-dump-key>Default search type</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default search type</span>\"\n                    \"<span class=sf-dump-key>Delete ads.txt file</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Delete ads.txt file</span>\"\n                    \"<span class=sf-dump-key>Delete property successfully!</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Delete property successfully!</span>\"\n                    \"<span class=sf-dump-key>Deposit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1044;&#1077;&#1087;&#1086;&#1079;&#1080;&#1090;</span>\"\n                    \"<span class=sf-dump-key>Description</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1054;&#1087;&#1080;&#1089;&#1072;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Destination type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Destination type</span>\"\n                    \"<span class=sf-dump-key>Detail Page (after)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Detail Page (after)</span>\"\n                    \"<span class=sf-dump-key>Detail Page (before)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Detail Page (before)</span>\"\n                    \"<span class=sf-dump-key>Detail Page Sidebar (after)</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Detail Page Sidebar (after)</span>\"\n                    \"<span class=sf-dump-key>Detail Page Sidebar (before)</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Detail Page Sidebar (before)</span>\"\n                    \"<span class=sf-dump-key>Dismiss</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1055;&#1088;&#1086;&#1087;&#1091;&#1089;&#1090;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Display Newsletter form on sidebar</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Display Newsletter form on sidebar</span>\"\n                    \"<span class=sf-dump-key>Display blog posts</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Display blog posts</span>\"\n                    \"<span class=sf-dump-key>Display on pages</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Display on pages</span>\"\n                    \"<span class=sf-dump-key>Display posts count?</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Display posts count?</span>\"\n                    \"<span class=sf-dump-key>Display recent blog posts</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Display recent blog posts</span>\"\n                    \"<span class=sf-dump-key>Display type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Display type</span>\"\n                    \"<span class=sf-dump-key>Displays a list of posts related to the current content.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Displays a list of posts related to the current content.</span>\"\n                    \"<span class=sf-dump-key>Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...</span>\" => \"<span class=sf-dump-str title=\"130 characters\">Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...</span>\"\n                    \"<span class=sf-dump-key>Distance key between facilities</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Distance key between facilities</span>\"\n                    \"<span class=sf-dump-key>Do you want to delete this image?</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Do you want to delete this image?</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t have an account?</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Don&#039;t have an account?</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t show this popup again</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Don&#039;t show this popup again</span>\"\n                    \"<span class=sf-dump-key>Dynamic carousel for featured content with customizable links.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Dynamic carousel for featured content with customizable links.</span>\"\n                    \"<span class=sf-dump-key>Edit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1048;&#1079;&#1084;&#1077;&#1085;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Edit listing</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Edit listing</span>\"\n                    \"<span class=sf-dump-key>Edit profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1055;&#1088;&#1086;&#1092;&#1080;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Edit this agent</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Edit this agent</span>\"\n                    \"<span class=sf-dump-key>Edit this project</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Edit this project</span>\"\n                    \"<span class=sf-dump-key>Edit this property</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Edit this property</span>\"\n                    \"<span class=sf-dump-key>Edit this shortcode</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Edit this shortcode</span>\"\n                    \"<span class=sf-dump-key>Edit this widget</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Edit this widget</span>\"\n                    \"<span class=sf-dump-key>Email</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>Email Address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email Address</span>\"\n                    \"<span class=sf-dump-key>Email address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email address</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook chat?</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Enable Facebook chat?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in post detail page?</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Enable Facebook comment in post detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in project detail page?</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Enable Facebook comment in project detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in property detail page?</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Enable Facebook comment in property detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enable Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Enable Preloader?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable Preloader?</span>\"\n                    \"<span class=sf-dump-key>Enable back to top button</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Enable back to top button</span>\"\n                    \"<span class=sf-dump-key>Enable dark mode</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enable dark mode</span>\"\n                    \"<span class=sf-dump-key>Enable lazy loading</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Enable lazy loading</span>\"\n                    \"<span class=sf-dump-key>Enable light mode</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable light mode</span>\"\n                    \"<span class=sf-dump-key>Enable search box</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable search box</span>\"\n                    \"<span class=sf-dump-key>Enable search projects on search box</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Enable search projects on search box</span>\"\n                    \"<span class=sf-dump-key>Enable sticky header</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enable sticky header</span>\"\n                    \"<span class=sf-dump-key>Enter API key into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Enter API key into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Client ID, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Enter Client ID, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Public, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Enter Public, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Your Email</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1042;&#1074;&#1077;&#1076;&#1080;&#1090;&#1077; &#1042;&#1072;&#1096; Email</span>\"\n                    \"<span class=sf-dump-key>Enter checklist here, saperated by commas (,)</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Enter checklist here, saperated by commas (,)</span>\"\n                    \"<span class=sf-dump-key>Enter keyword...</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter keyword...</span>\"\n                    \"<span class=sf-dump-key>Enter your message</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1042;&#1072;&#1096;&#1077; &#1089;&#1086;&#1086;&#1073;&#1097;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Enter your message...</span>\" => \"<span class=sf-dump-str title=\"18 characters\">&#1042;&#1072;&#1096;&#1077; &#1089;&#1086;&#1086;&#1073;&#1097;&#1077;&#1085;&#1080;&#1077; ...</span>\"\n                    \"<span class=sf-dump-key>Error</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1054;&#1096;&#1080;&#1073;&#1082;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Error when processing payment via :paymentType!</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Error when processing payment via :paymentType!</span>\"\n                    \"<span class=sf-dump-key>Ex: 60-Day Job Postings</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Ex: 60-Day Job Postings</span>\"\n                    \"<span class=sf-dump-key>Expand the content of the first FAQ</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Expand the content of the first FAQ</span>\"\n                    \"<span class=sf-dump-key>Explore Now</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Explore Now</span>\"\n                    \"<span class=sf-dump-key>Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property&#039;s convenience.</span>\" => \"<span class=sf-dump-str>-</span>\"\n                    \"<span class=sf-dump-key>Explore the World&#8217;s Properties for Rent</span>\" => \"<span class=sf-dump-str title=\"46 characters\">&#1044;&#1086;&#1083;&#1075;&#1086;&#1089;&#1088;&#1086;&#1095;&#1085;&#1072;&#1103; &#1072;&#1088;&#1077;&#1085;&#1076;&#1072; &#1085;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1080; &#1087;&#1086; &#1074;&#1089;&#1077;&#1084;&#1091; &#1084;&#1080;&#1088;&#1091;</span>\"\n                    \"<span class=sf-dump-key>FAQ categories</span>\" => \"<span class=sf-dump-str title=\"14 characters\">FAQ categories</span>\"\n                    \"<span class=sf-dump-key>FAQs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FAQs</span>\"\n                    \"<span class=sf-dump-key>Facebook</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Facebook</span>\"\n                    \"<span class=sf-dump-key>Facebook Admin ID</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Facebook Admin ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Admins</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook Admins</span>\"\n                    \"<span class=sf-dump-key>Facebook App ID</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook App ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Integration</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facebook Integration</span>\"\n                    \"<span class=sf-dump-key>Facebook admins to manage comments :link</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Facebook admins to manage comments :link</span>\"\n                    \"<span class=sf-dump-key>Facebook page ID</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Facebook page ID</span>\"\n                    \"<span class=sf-dump-key>Featured</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Featured</span>\"\n                    \"<span class=sf-dump-key>Featured Amenity</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Featured Amenity</span>\"\n                    \"<span class=sf-dump-key>Featured projects</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Featured projects</span>\"\n                    \"<span class=sf-dump-key>Featured properties</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Featured properties</span>\"\n                    \"<span class=sf-dump-key>Features</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Features</span>\"\n                    \"<span class=sf-dump-key>Filter</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1060;&#1080;&#1083;&#1100;&#1090;&#1088;</span>\"\n                    \"<span class=sf-dump-key>Filter box on the left</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Filter box on the left</span>\"\n                    \"<span class=sf-dump-key>Filters</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1060;&#1080;&#1083;&#1100;&#1090;&#1088;</span>\"\n                    \"<span class=sf-dump-key>Find Projects</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Find Projects</span>\"\n                    \"<span class=sf-dump-key>Find Properties</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Find Properties</span>\"\n                    \"<span class=sf-dump-key>First name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">First name</span>\"\n                    \"<span class=sf-dump-key>First time on the site?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1042;&#1087;&#1077;&#1088;&#1074;&#1099;&#1077; &#1085;&#1072; &#1089;&#1072;&#1081;&#1090;&#1077;?</span>\"\n                    \"<span class=sf-dump-key>Fix it for me</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Fix it for me</span>\"\n                    \"<span class=sf-dump-key>Flat Range</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Flat Range</span>\"\n                    \"<span class=sf-dump-key>Flat from</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat from</span>\"\n                    \"<span class=sf-dump-key>Flat to</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Flat to</span>\"\n                    \"<span class=sf-dump-key>Flats:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1050;&#1086;&#1084;&#1085;&#1072;&#1090;:</span>\"\n                    \"<span class=sf-dump-key>Floor Plans</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Floor Plans</span>\"\n                    \"<span class=sf-dump-key>Floor plans</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Floor plans</span>\"\n                    \"<span class=sf-dump-key>Floors</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Floors</span>\"\n                    \"<span class=sf-dump-key>Floors:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Floors:</span>\"\n                    \"<span class=sf-dump-key>Follow Us</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1055;&#1088;&#1080;&#1089;&#1086;&#1077;&#1076;&#1080;&#1085;&#1103;&#1081;&#1090;&#1077;&#1089;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Follow Us:</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1055;&#1088;&#1080;&#1089;&#1086;&#1077;&#1076;&#1080;&#1085;&#1103;&#1081;&#1090;&#1077;&#1089;&#1100;:</span>\"\n                    \"<span class=sf-dump-key>Footer (after)</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Footer (after)</span>\"\n                    \"<span class=sf-dump-key>Footer (before)</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Footer (before)</span>\"\n                    \"<span class=sf-dump-key>Footer background color</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Footer background color</span>\"\n                    \"<span class=sf-dump-key>Footer background image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Footer background image</span>\"\n                    \"<span class=sf-dump-key>For Rent</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1040;&#1088;&#1077;&#1085;&#1076;&#1072;</span>\"\n                    \"<span class=sf-dump-key>For Sale</span>\" => \"<span class=sf-dump-str title=\"8 characters\">For Sale</span>\"\n                    \"<span class=sf-dump-key>For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\" => \"<span class=sf-dump-str title=\"91 characters\">For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\"\n                    \"<span class=sf-dump-key>For devices with width less than 768px, if empty, will use the image from the tablet.</span>\" => \"<span class=sf-dump-str title=\"85 characters\">For devices with width less than 768px, if empty, will use the image from the tablet.</span>\"\n                    \"<span class=sf-dump-key>Forgot Password</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1053;&#1072;&#1087;&#1086;&#1084;&#1085;&#1080;&#1090;&#1100; &#1087;&#1072;&#1088;&#1086;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Forgot password?</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1047;&#1072;&#1073;&#1080;&#1083;&#1099; &#1087;&#1072;&#1088;&#1086;&#1083;&#1100;?</span>\"\n                    \"<span class=sf-dump-key>Found</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1053;&#1072;&#1081;&#1076;&#1077;&#1085;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Free</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Free</span>\"\n                    \"<span class=sf-dump-key>Free :number post(s)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Free :number post(s)</span>\"\n                    \"<span class=sf-dump-key>From</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1044;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Full Width</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Full Width</span>\"\n                    \"<span class=sf-dump-key>Functions</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Functions</span>\"\n                    \"<span class=sf-dump-key>Gallery</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1043;&#1072;&#1083;&#1083;&#1077;&#1088;&#1077;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Go to :link to change the copyright text.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Go to :link to change the copyright text.</span>\"\n                    \"<span class=sf-dump-key>Go to homepage</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Go to homepage</span>\"\n                    \"<span class=sf-dump-key>Google Maps</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Google Maps</span>\"\n                    \"<span class=sf-dump-key>Grid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Grid</span>\"\n                    \"<span class=sf-dump-key>Group by category</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Group by category</span>\"\n                    \"<span class=sf-dump-key>HTML code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">HTML code</span>\"\n                    \"<span class=sf-dump-key>Have an account?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1059;&#1078;&#1077; &#1077;&#1089;&#1090;&#1100; &#1072;&#1082;&#1082;&#1072;&#1091;&#1085;&#1090;?</span>\"\n                    \"<span class=sf-dump-key>Header (after)</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Header (after)</span>\"\n                    \"<span class=sf-dump-key>Header (before)</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Header (before)</span>\"\n                    \"<span class=sf-dump-key>Heading</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Heading</span>\"\n                    \"<span class=sf-dump-key>Heading 1</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 1</span>\"\n                    \"<span class=sf-dump-key>Heading 2</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 2</span>\"\n                    \"<span class=sf-dump-key>Heading 3</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 3</span>\"\n                    \"<span class=sf-dump-key>Heading 4</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 4</span>\"\n                    \"<span class=sf-dump-key>Heading 5</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 5</span>\"\n                    \"<span class=sf-dump-key>Heading 6</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 6</span>\"\n                    \"<span class=sf-dump-key>Height</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Height</span>\"\n                    \"<span class=sf-dump-key>Hello</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hello</span>\"\n                    \"<span class=sf-dump-key>Hero Banner</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hero Banner</span>\"\n                    \"<span class=sf-dump-key>Hide Advanced</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Hide Advanced</span>\"\n                    \"<span class=sf-dump-key>Home</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1043;&#1083;&#1072;&#1074;&#1085;&#1072;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Homepage</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1043;&#1083;&#1072;&#1074;&#1085;&#1072;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Hotline</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hotline</span>\"\n                    \"<span class=sf-dump-key>Hours</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hours</span>\"\n                    \"<span class=sf-dump-key>Hover color</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hover color</span>\"\n                    \"<span class=sf-dump-key>I agree to the :link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">I agree to the :link</span>\"\n                    \"<span class=sf-dump-key>I agree to the Terms and Privacy Policy</span>\" => \"<span class=sf-dump-str title=\"39 characters\">I agree to the Terms and Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>Icon</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Icon</span>\"\n                    \"<span class=sf-dump-key>Icon Image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon Image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>Icon image</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Icon image</span>\"\n                    \"<span class=sf-dump-key>Icon image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.</span>\" => \"<span class=sf-dump-str title=\"109 characters\">If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.</span>\"\n                    \"<span class=sf-dump-key>If icon image is set, it will be used instead of the icon above.</span>\" => \"<span class=sf-dump-str title=\"64 characters\">If icon image is set, it will be used instead of the icon above.</span>\"\n                    \"<span class=sf-dump-key>If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\" => \"<span class=sf-dump-str title=\"187 characters\">If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\"\n                    \"<span class=sf-dump-key>If you need help, contact us at :mail.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">If you need help, contact us at :mail.</span>\"\n                    \"<span class=sf-dump-key>If you select an image, the background color will be ignored.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">If you select an image, the background color will be ignored.</span>\"\n                    \"<span class=sf-dump-key>If you use the YouTube video link above, the thumbnail will be automatically obtained.</span>\" => \"<span class=sf-dump-str title=\"86 characters\">If you use the YouTube video link above, the thumbnail will be automatically obtained.</span>\"\n                    \"<span class=sf-dump-key>Image</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n                    \"<span class=sf-dump-key>Image Slider</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image Slider</span>\"\n                    \"<span class=sf-dump-key>Information box title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Information box title</span>\"\n                    \"<span class=sf-dump-key>Inner Footer Sidebar</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Inner Footer Sidebar</span>\"\n                    \"<span class=sf-dump-key>Inner footer section for site info, menus, and newsletter.</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Inner footer section for site info, menus, and newsletter.</span>\"\n                    \"<span class=sf-dump-key>Install plugin from Marketplace</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Install plugin from Marketplace</span>\"\n                    \"<span class=sf-dump-key>Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>Invalid Data!</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid Data!</span>\"\n                    \"<span class=sf-dump-key>Invalid Transaction!</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invalid Transaction!</span>\"\n                    \"<span class=sf-dump-key>Invalid step.</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid step.</span>\"\n                    \"<span class=sf-dump-key>InvalidStateException occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"52 characters\">InvalidStateException occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Investor</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Investor</span>\"\n                    \"<span class=sf-dump-key>Investor:</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Investor:</span>\"\n                    \"<span class=sf-dump-key>Invoice detail :code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invoice detail :code</span>\"\n                    \"<span class=sf-dump-key>Invoice information from database, ex: invoice.code, invoice.amount, ...</span>\" => \"<span class=sf-dump-str title=\"72 characters\">Invoice information from database, ex: invoice.code, invoice.amount, ...</span>\"\n                    \"<span class=sf-dump-key>Invoices</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Invoices</span>\"\n                    \"<span class=sf-dump-key>Is autoplay?</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Is autoplay?</span>\"\n                    \"<span class=sf-dump-key>It looks as through there are no activities here.</span>\" => \"<span class=sf-dump-str title=\"49 characters\">It looks as through there are no activities here.</span>\"\n                    \"<span class=sf-dump-key>It will replace Icon Font if it is present.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">It will replace Icon Font if it is present.</span>\"\n                    \"<span class=sf-dump-key>Items per row</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Items per row</span>\"\n                    \"<span class=sf-dump-key>Job summary</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Job summary</span>\"\n                    \"<span class=sf-dump-key>Jobs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Jobs</span>\"\n                    \"<span class=sf-dump-key>Joined</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Joined</span>\"\n                    \"<span class=sf-dump-key>Joined on :date</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Joined on :date</span>\"\n                    \"<span class=sf-dump-key>Key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Key</span>\"\n                    \"<span class=sf-dump-key>Keyword</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Keyword</span>\"\n                    \"<span class=sf-dump-key>Label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Label</span>\"\n                    \"<span class=sf-dump-key>Last Updated</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Last Updated</span>\"\n                    \"<span class=sf-dump-key>Last name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Last name</span>\"\n                    \"<span class=sf-dump-key>Latest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Latest</span>\"\n                    \"<span class=sf-dump-key>Latest Properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Latest Properties</span>\"\n                    \"<span class=sf-dump-key>Latest posts from :site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Latest posts from :site_title</span>\"\n                    \"<span class=sf-dump-key>Latitude longitude center on properties page</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Latitude longitude center on properties page</span>\"\n                    \"<span class=sf-dump-key>Lazy load images</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Lazy load images</span>\"\n                    \"<span class=sf-dump-key>Lazy load placeholder image</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Lazy load placeholder image</span>\"\n                    \"<span class=sf-dump-key>Learn More</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1086;&#1076;&#1088;&#1086;&#1073;&#1085;&#1077;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Learn more</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1086;&#1076;&#1088;&#1086;&#1073;&#1085;&#1077;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Learn more about Twig template: :url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Learn more about Twig template: :url</span>\"\n                    \"<span class=sf-dump-key>Leave categories empty if you want to show posts from all categories.</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Leave categories empty if you want to show posts from all categories.</span>\"\n                    \"<span class=sf-dump-key>License</span>\" => \"<span class=sf-dump-str title=\"7 characters\">License</span>\"\n                    \"<span class=sf-dump-key>License Activation</span>\" => \"<span class=sf-dump-str title=\"18 characters\">License Activation</span>\"\n                    \"<span class=sf-dump-key>Limit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Limit</span>\"\n                    \"<span class=sf-dump-key>LinkedIn</span>\" => \"<span class=sf-dump-str title=\"8 characters\">LinkedIn</span>\"\n                    \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n                    \"<span class=sf-dump-key>List with map on the right</span>\" => \"<span class=sf-dump-str title=\"26 characters\">List with map on the right</span>\"\n                    \"<span class=sf-dump-key>List with map on top</span>\" => \"<span class=sf-dump-str title=\"20 characters\">List with map on top</span>\"\n                    \"<span class=sf-dump-key>Listing Page (after)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Listing Page (after)</span>\"\n                    \"<span class=sf-dump-key>Listing Page (before)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Listing Page (before)</span>\"\n                    \"<span class=sf-dump-key>Location</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1051;&#1086;&#1082;&#1072;&#1094;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Location type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1058;&#1080;&#1087; &#1083;&#1086;&#1082;&#1072;&#1094;&#1080;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Log in</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1042;&#1086;&#1081;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Log out</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1042;&#1099;&#1081;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Login</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1042;&#1086;&#1081;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Login to your account</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Login to your account</span>\"\n                    \"<span class=sf-dump-key>Login with social networks</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Login with social networks</span>\"\n                    \"<span class=sf-dump-key>Logo height (px)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Logo height (px)</span>\"\n                    \"<span class=sf-dump-key>Logo light</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Logo light</span>\"\n                    \"<span class=sf-dump-key>Looks like there are no reviews!</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Looks like there are no reviews!</span>\"\n                    \"<span class=sf-dump-key>Loop?</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Loop?</span>\"\n                    \"<span class=sf-dump-key>Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\" => \"<span class=sf-dump-str title=\"124 characters\">Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\"\n                    \"<span class=sf-dump-key>Main header background color</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Main header background color</span>\"\n                    \"<span class=sf-dump-key>Main header border color</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Main header border color</span>\"\n                    \"<span class=sf-dump-key>Main header text color</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Main header text color</span>\"\n                    \"<span class=sf-dump-key>Manage Invoices</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Manage Invoices</span>\"\n                    \"<span class=sf-dump-key>Manage Social Links in Appearance &#8594; Theme Options &#8594; Social links.</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Manage Social Links in Appearance &#8594; Theme Options &#8594; Social links.</span>\"\n                    \"<span class=sf-dump-key>Manage the social links in Theme Options -&gt; Social Links</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Manage the social links in Theme Options -&gt; Social Links</span>\"\n                    \"<span class=sf-dump-key>Manual Transaction</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Manual Transaction</span>\"\n                    \"<span class=sf-dump-key>Map</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1050;&#1072;&#1088;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Math Captcha</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Math Captcha</span>\"\n                    \"<span class=sf-dump-key>Math Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Math Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Media - Audio</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Audio</span>\"\n                    \"<span class=sf-dump-key>Media - Video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Video</span>\"\n                    \"<span class=sf-dump-key>Media URL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Media URL</span>\"\n                    \"<span class=sf-dump-key>Menu</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1052;&#1077;&#1085;&#1102;</span>\"\n                    \"<span class=sf-dump-key>Message</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1057;&#1086;&#1086;&#1073;&#1097;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Minutes</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Minutes</span>\"\n                    \"<span class=sf-dump-key>Mobile Image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mobile Image</span>\"\n                    \"<span class=sf-dump-key>More Job Openings</span>\" => \"<span class=sf-dump-str title=\"17 characters\">More Job Openings</span>\"\n                    \"<span class=sf-dump-key>More properties by this agent</span>\" => \"<span class=sf-dump-str title=\"29 characters\">More properties by this agent</span>\"\n                    \"<span class=sf-dump-key>My Profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1055;&#1088;&#1086;&#1092;&#1080;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>My Wishlist</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1048;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>My favorites</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1048;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>My listings</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1052;&#1086;&#1080; &#1086;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1048;&#1084;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Name (A-Z)</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Name (A-Z)</span>\"\n                    \"<span class=sf-dump-key>Name (Z-A)</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Name (Z-A)</span>\"\n                    \"<span class=sf-dump-key>Nearby</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Nearby</span>\"\n                    \"<span class=sf-dump-key>Newest</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1053;&#1086;&#1074;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Newsletter form</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Newsletter form</span>\"\n                    \"<span class=sf-dump-key>Next</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1044;&#1072;&#1083;&#1077;&#1077;</span>\"\n                    \"<span class=sf-dump-key>No</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1053;&#1077;&#1090;</span>\"\n                    \"<span class=sf-dump-key>No Layout</span>\" => \"<span class=sf-dump-str title=\"9 characters\">No Layout</span>\"\n                    \"<span class=sf-dump-key>No cities found</span>\" => \"<span class=sf-dump-str title=\"15 characters\">No cities found</span>\"\n                    \"<span class=sf-dump-key>No payment charge. Please try again!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">No payment charge. Please try again!</span>\"\n                    \"<span class=sf-dump-key>No project found</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &#1085;&#1077; &#1085;&#1072;&#1081;&#1076;&#1077;&#1085;</span>\"\n                    \"<span class=sf-dump-key>No projects found.</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#1046;&#1080;&#1083;&#1099;&#1093; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;&#1086;&#1074; &#1085;&#1077; &#1085;&#1072;&#1081;&#1076;&#1077;&#1085;&#1086;</span>\"\n                    \"<span class=sf-dump-key>No properties found.</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1054;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1081; &#1085;&#1077; &#1085;&#1072;&#1081;&#1076;&#1077;&#1085;&#1086;</span>\"\n                    \"<span class=sf-dump-key>No results found</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1053;&#1080;&#1095;&#1077;&#1075;&#1086; &#1085;&#1077; &#1085;&#1072;&#1081;&#1076;&#1077;&#1085;&#1086;</span>\"\n                    \"<span class=sf-dump-key>No suggestion found</span>\" => \"<span class=sf-dump-str title=\"19 characters\">No suggestion found</span>\"\n                    \"<span class=sf-dump-key>No transactions!</span>\" => \"<span class=sf-dump-str title=\"16 characters\">No transactions!</span>\"\n                    \"<span class=sf-dump-key>Number</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Number</span>\"\n                    \"<span class=sf-dump-key>Number of bathrooms</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1050;&#1086;&#1083;-&#1090;&#1074;&#1072; &#1074;&#1072;&#1085;&#1085;&#1099;&#1093;</span>\"\n                    \"<span class=sf-dump-key>Number of bedrooms</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1050;&#1086;&#1083;-&#1090;&#1074;&#1086; &#1089;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Number of blocks</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Number of blocks</span>\"\n                    \"<span class=sf-dump-key>Number of columns</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Number of columns</span>\"\n                    \"<span class=sf-dump-key>Number of credits</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Number of credits</span>\"\n                    \"<span class=sf-dump-key>Number of flats</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Number of flats</span>\"\n                    \"<span class=sf-dump-key>Number of floors</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Number of floors</span>\"\n                    \"<span class=sf-dump-key>Number of items to display</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Number of items to display</span>\"\n                    \"<span class=sf-dump-key>Number of projects per page</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Number of projects per page</span>\"\n                    \"<span class=sf-dump-key>Number of properties per page</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Number of properties per page</span>\"\n                    \"<span class=sf-dump-key>Number of related projects</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Number of related projects</span>\"\n                    \"<span class=sf-dump-key>Number of related properties</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Number of related properties</span>\"\n                    \"<span class=sf-dump-key>Number tags to display</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Number tags to display</span>\"\n                    \"<span class=sf-dump-key>OK</span>\" => \"<span class=sf-dump-str title=\"2 characters\">OK</span>\"\n                    \"<span class=sf-dump-key>Object type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1058;&#1080;&#1087; &#1086;&#1073;&#1100;&#1077;&#1082;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Oldest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Oldest</span>\"\n                    \"<span class=sf-dump-key>Only show featured properties</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Only show featured properties</span>\"\n                    \"<span class=sf-dump-key>Oops&#8230; You just found an error page!</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Oops&#8230; You just found an error page!</span>\"\n                    \"<span class=sf-dump-key>Open URL in a new tab</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Open URL in a new tab</span>\"\n                    \"<span class=sf-dump-key>Open user menu</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Open user menu</span>\"\n                    \"<span class=sf-dump-key>Overview</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1054;&#1073;&#1079;&#1086;&#1088;</span>\"\n                    \"<span class=sf-dump-key>PHP version :version required</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP version :version required</span>\"\n                    \"<span class=sf-dump-key>Package information</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Package information</span>\"\n                    \"<span class=sf-dump-key>Packages</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Packages</span>\"\n                    \"<span class=sf-dump-key>Page could not be found</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Page could not be found</span>\"\n                    \"<span class=sf-dump-key>Password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1055;&#1072;&#1088;&#1086;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Password confirmation</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Password confirmation</span>\"\n                    \"<span class=sf-dump-key>Payment Type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Payment Type</span>\"\n                    \"<span class=sf-dump-key>Payment description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Payment description</span>\"\n                    \"<span class=sf-dump-key>Payment failed!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Payment failed!</span>\"\n                    \"<span class=sf-dump-key>Payment method</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment method</span>\"\n                    \"<span class=sf-dump-key>Payment status</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Payment status</span>\"\n                    \"<span class=sf-dump-key>Payment with :paymentType</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Payment with :paymentType</span>\"\n                    \"<span class=sf-dump-key>Phone</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1058;&#1077;&#1083;&#1077;&#1092;&#1086;&#1085;</span>\"\n                    \"<span class=sf-dump-key>Phone (optional)</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1058;&#1077;&#1083;&#1077;&#1092;&#1086;&#1085; (&#1085;&#1077; &#1086;&#1073;&#1103;&#1079;&#1072;&#1090;&#1077;&#1083;&#1100;&#1085;&#1086;)</span>\"\n                    \"<span class=sf-dump-key>Phone number</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1058;&#1077;&#1083;&#1077;&#1092;&#1086;&#1085;</span>\"\n                    \"<span class=sf-dump-key>Pinterest</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Pinterest</span>\"\n                    \"<span class=sf-dump-key>Place widgets here to display additional content below individual blog posts.</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Place widgets here to display additional content below individual blog posts.</span>\"\n                    \"<span class=sf-dump-key>Please log in to write review!</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Please log in to write review!</span>\"\n                    \"<span class=sf-dump-key>Please solve the following math function: :label = ?</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Please solve the following math function: :label = ?</span>\"\n                    \"<span class=sf-dump-key>Please switch currency to any supported currency</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Please switch currency to any supported currency</span>\"\n                    \"<span class=sf-dump-key>Please try again in a few minutes, or alternatively return to the homepage by &lt;a href=&quot;:link&quot;&gt;clicking here&lt;/a&gt;.</span>\" => \"<span class=sf-dump-str title=\"112 characters\">Please try again in a few minutes, or alternatively return to the homepage by &lt;a href=&quot;:link&quot;&gt;clicking here&lt;/a&gt;.</span>\"\n                    \"<span class=sf-dump-key>Popular</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1055;&#1086;&#1087;&#1091;&#1083;&#1103;&#1088;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Popular tags</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Popular tags</span>\"\n                    \"<span class=sf-dump-key>Popup Delay (seconds)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Popup Delay (seconds)</span>\"\n                    \"<span class=sf-dump-key>Popup Description</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Popup Description</span>\"\n                    \"<span class=sf-dump-key>Popup Image</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Popup Image</span>\"\n                    \"<span class=sf-dump-key>Popup Subtitle</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Popup Subtitle</span>\"\n                    \"<span class=sf-dump-key>Popup Title</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Popup Title</span>\"\n                    \"<span class=sf-dump-key>Post Detail (after)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Post Detail (after)</span>\"\n                    \"<span class=sf-dump-key>Post Detail (before)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Post Detail (before)</span>\"\n                    \"<span class=sf-dump-key>Post type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Post type</span>\"\n                    \"<span class=sf-dump-key>Posted at</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Posted at</span>\"\n                    \"<span class=sf-dump-key>Posts</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Posts</span>\"\n                    \"<span class=sf-dump-key>Preloader Version</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Preloader Version</span>\"\n                    \"<span class=sf-dump-key>Previous</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1053;&#1072;&#1079;&#1072;&#1076;</span>\"\n                    \"<span class=sf-dump-key>Price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1062;&#1077;&#1085;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Price $/month</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1062;&#1077;&#1085;&#1072; $/&#1084;&#1077;&#1089;</span>\"\n                    \"<span class=sf-dump-key>Price (high to low)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Price (high to low)</span>\"\n                    \"<span class=sf-dump-key>Price (low to high)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Price (low to high)</span>\"\n                    \"<span class=sf-dump-key>Price Range</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Price Range</span>\"\n                    \"<span class=sf-dump-key>Price from</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1062;&#1077;&#1085;&#1072; &#1086;&#1090;</span>\"\n                    \"<span class=sf-dump-key>Price to</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1062;&#1077;&#1085;&#1072; &#1076;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Price:</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1062;&#1077;&#1085;&#1072;:</span>\"\n                    \"<span class=sf-dump-key>Pricing Plan</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Pricing Plan</span>\"\n                    \"<span class=sf-dump-key>Primary</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Primary</span>\"\n                    \"<span class=sf-dump-key>Primary color</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Primary color</span>\"\n                    \"<span class=sf-dump-key>Private Notes</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Private Notes</span>\"\n                    \"<span class=sf-dump-key>Processing. Please wait...</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Processing. Please wait...</span>\"\n                    \"<span class=sf-dump-key>Products</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Products</span>\"\n                    \"<span class=sf-dump-key>Profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1055;&#1088;&#1086;&#1092;&#1080;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Project</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;</span>\"\n                    \"<span class=sf-dump-key>Project ID</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Project ID</span>\"\n                    \"<span class=sf-dump-key>Project ID:</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Project ID:</span>\"\n                    \"<span class=sf-dump-key>Project listing page layout</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Project listing page layout</span>\"\n                    \"<span class=sf-dump-key>Project video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Project video</span>\"\n                    \"<span class=sf-dump-key>Project&#039;s Information</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Project&#039;s Information</span>\"\n                    \"<span class=sf-dump-key>Project&#039;s information</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Project&#039;s information</span>\"\n                    \"<span class=sf-dump-key>Projects</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1046;&#1080;&#1083;&#1099;&#1077; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Projects List</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Projects List</span>\"\n                    \"<span class=sf-dump-key>Projects List page</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Projects List page</span>\"\n                    \"<span class=sf-dump-key>Projects in :city</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Projects in :city</span>\"\n                    \"<span class=sf-dump-key>Projects in :state</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Projects in :state</span>\"\n                    \"<span class=sf-dump-key>Properties</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1053;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Properties For Rent</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1040;&#1088;&#1077;&#1085;&#1076;&#1072; &#1085;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Properties For Sale</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Properties For Sale</span>\"\n                    \"<span class=sf-dump-key>Properties List</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Properties List</span>\"\n                    \"<span class=sf-dump-key>Properties List page</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Properties List page</span>\"\n                    \"<span class=sf-dump-key>Properties by this agent</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1054;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1103; &#1072;&#1074;&#1090;&#1086;&#1088;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Properties in :city</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Properties in :city</span>\"\n                    \"<span class=sf-dump-key>Properties in :name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Properties in :name</span>\"\n                    \"<span class=sf-dump-key>Properties in :state</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Properties in :state</span>\"\n                    \"<span class=sf-dump-key>Properties in project &quot;:name&quot;</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Properties in project &quot;:name&quot;</span>\"\n                    \"<span class=sf-dump-key>Property</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1053;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Property Categories</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Property Categories</span>\"\n                    \"<span class=sf-dump-key>Property ID</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Property ID</span>\"\n                    \"<span class=sf-dump-key>Property ID:</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Property ID:</span>\"\n                    \"<span class=sf-dump-key>Property detail page layout</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Property detail page layout</span>\"\n                    \"<span class=sf-dump-key>Property for rent</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1040;&#1088;&#1077;&#1085;&#1076;&#1072; &#1085;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Property for sale</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Property for sale</span>\"\n                    \"<span class=sf-dump-key>Property listing page layout</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Property listing page layout</span>\"\n                    \"<span class=sf-dump-key>Property video</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Property video</span>\"\n                    \"<span class=sf-dump-key>Public Key</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Public Key</span>\"\n                    \"<span class=sf-dump-key>Quantity</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Quantity</span>\"\n                    \"<span class=sf-dump-key>Read More</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1086;&#1076;&#1088;&#1086;&#1073;&#1085;&#1077;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Read more</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1086;&#1076;&#1088;&#1086;&#1073;&#1085;&#1077;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Real Estate</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1053;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Recent</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Recent</span>\"\n                    \"<span class=sf-dump-key>Recent Posts</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Recent Posts</span>\"\n                    \"<span class=sf-dump-key>Recover</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Recover</span>\"\n                    \"<span class=sf-dump-key>Register</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1056;&#1077;&#1075;&#1080;&#1089;&#1090;&#1088;&#1072;&#1094;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Register an account</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Register an account</span>\"\n                    \"<span class=sf-dump-key>Register an account on :name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Register an account on :name</span>\"\n                    \"<span class=sf-dump-key>Register now</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1056;&#1077;&#1075;&#1080;&#1089;&#1090;&#1088;&#1072;&#1094;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Registered successfully!</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Registered successfully!</span>\"\n                    \"<span class=sf-dump-key>Related Careers</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Related Careers</span>\"\n                    \"<span class=sf-dump-key>Related Posts</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Related Posts</span>\"\n                    \"<span class=sf-dump-key>Related properties</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Related properties</span>\"\n                    \"<span class=sf-dump-key>Remember me</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1047;&#1072;&#1087;&#1086;&#1084;&#1085;&#1080;&#1090;&#1100; &#1084;&#1077;&#1085;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Remove</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1059;&#1076;&#1072;&#1083;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Remove image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1059;&#1076;&#1072;&#1083;&#1080;&#1090;&#1100; &#1092;&#1086;&#1090;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Removed &quot;:name&quot; from wishlist successfully!</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Removed &quot;:name&quot; from wishlist successfully!</span>\"\n                    \"<span class=sf-dump-key>Removed :credits credit(s) by admin &quot;:user&quot;</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Removed :credits credit(s) by admin &quot;:user&quot;</span>\"\n                    \"<span class=sf-dump-key>Removed coupon :code successfully!</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Removed coupon :code successfully!</span>\"\n                    \"<span class=sf-dump-key>Renew</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Renew</span>\"\n                    \"<span class=sf-dump-key>Renew confirmation</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Renew confirmation</span>\"\n                    \"<span class=sf-dump-key>Renew property successfully</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Renew property successfully</span>\"\n                    \"<span class=sf-dump-key>Rent Apartment</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1058;&#1080;&#1087; &#1086;&#1073;&#1100;&#1077;&#1082;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Rented</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1057;&#1076;&#1072;&#1085;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Report</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1055;&#1086;&#1078;&#1072;&#1083;&#1086;&#1074;&#1072;&#1090;&#1100;&#1089;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Reset</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1057;&#1073;&#1088;&#1086;&#1089;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Reset Password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1057;&#1073;&#1088;&#1086;&#1089;&#1080;&#1090;&#1100; &#1087;&#1072;&#1088;&#1086;&#1083;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Review</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1054;&#1090;&#1079;&#1099;&#1074;</span>\"\n                    \"<span class=sf-dump-key>Reviews</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1054;&#1090;&#1079;&#1099;&#1074;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Run</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Run</span>\"\n                    \"<span class=sf-dump-key>Salary</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Salary</span>\"\n                    \"<span class=sf-dump-key>Search</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1055;&#1086;&#1080;&#1089;&#1082;</span>\"\n                    \"<span class=sf-dump-key>Search blog posts</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Search blog posts</span>\"\n                    \"<span class=sf-dump-key>Search by parameters</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Search by parameters</span>\"\n                    \"<span class=sf-dump-key>Search for Keyword</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Search for Keyword</span>\"\n                    \"<span class=sf-dump-key>Search for Location</span>\" => \"<span class=sf-dump-str title=\"16 characters\">&#1055;&#1086;&#1080;&#1089;&#1082; &#1087;&#1086; &#1083;&#1086;&#1082;&#1072;&#1094;&#1080;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Search project</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Search project</span>\"\n                    \"<span class=sf-dump-key>Search result for: &quot;:query&quot;</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Search result for: &quot;:query&quot;</span>\"\n                    \"<span class=sf-dump-key>Search...</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1055;&#1086;&#1080;&#1089;&#1082; ...</span>\"\n                    \"<span class=sf-dump-key>Seconds</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Seconds</span>\"\n                    \"<span class=sf-dump-key>Secret</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Secret</span>\"\n                    \"<span class=sf-dump-key>Secret Key</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Secret Key</span>\"\n                    \"<span class=sf-dump-key>Select a property to display on the banner.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Select a property to display on the banner.</span>\"\n                    \"<span class=sf-dump-key>Select background image for this section.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Select background image for this section.</span>\"\n                    \"<span class=sf-dump-key>Select categories</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1042;&#1099;&#1073;&#1088;&#1072;&#1090;&#1100; &#1082;&#1072;&#1090;&#1077;&#1075;&#1086;&#1088;&#1080;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Select categories to display as tabs.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Select categories to display as tabs.</span>\"\n                    \"<span class=sf-dump-key>Selecting an option will redirect you to a list of properties or projects page.</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Selecting an option will redirect you to a list of properties or projects page.</span>\"\n                    \"<span class=sf-dump-key>Send</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1054;&#1090;&#1087;&#1088;&#1072;&#1074;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Send Message</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1054;&#1090;&#1087;&#1088;&#1072;&#1074;&#1080;&#1090;&#1100; &#1089;&#1086;&#1086;&#1073;&#1097;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Send Password Reset Link</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Send Password Reset Link</span>\"\n                    \"<span class=sf-dump-key>Send Review</span>\" => \"<span class=sf-dump-str title=\"15 characters\">&#1054;&#1090;&#1087;&#1088;&#1072;&#1074;&#1080;&#1090;&#1100; &#1086;&#1090;&#1079;&#1099;&#1074;</span>\"\n                    \"<span class=sf-dump-key>Send message successfully!</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Send message successfully!</span>\"\n                    \"<span class=sf-dump-key>Separated by commas (,)</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Separated by commas (,)</span>\"\n                    \"<span class=sf-dump-key>Services</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1059;&#1089;&#1083;&#1091;&#1075;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.</span>\" => \"<span class=sf-dump-str title=\"99 characters\">Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.</span>\"\n                    \"<span class=sf-dump-key>Set the height of the logo in pixels. The default value is :default.</span>\" => \"<span class=sf-dump-str title=\"68 characters\">Set the height of the logo in pixels. The default value is :default.</span>\"\n                    \"<span class=sf-dump-key>Setup license code</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Setup license code</span>\"\n                    \"<span class=sf-dump-key>Share on :social</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Share on :social</span>\"\n                    \"<span class=sf-dump-key>Share this project</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Share this project</span>\"\n                    \"<span class=sf-dump-key>Share this property</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Share this property</span>\"\n                    \"<span class=sf-dump-key>Share:</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1055;&#1086;&#1076;&#1077;&#1083;&#1080;&#1090;&#1100;&#1089;&#1103;:</span>\"\n                    \"<span class=sf-dump-key>Show author name?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Show author name?</span>\"\n                    \"<span class=sf-dump-key>Show information box</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Show information box</span>\"\n                    \"<span class=sf-dump-key>Show map on the property/project detail page</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Show map on the property/project detail page</span>\"\n                    \"<span class=sf-dump-key>Show social links</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Show social links</span>\"\n                    \"<span class=sf-dump-key>Showing</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Showing</span>\"\n                    \"<span class=sf-dump-key>Sign in</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1042;&#1086;&#1081;&#1090;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Sign up</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#1056;&#1077;&#1075;&#1080;&#1089;&#1090;&#1088;&#1072;&#1094;&#1080;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Similar listings</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1057;&#1084;&#1086;&#1090;&#1088;&#1080;&#1090;&#1077; &#1090;&#1072;&#1082;&#1078;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Site Copyright</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Site Copyright</span>\"\n                    \"<span class=sf-dump-key>Site Language</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1071;&#1079;&#1099;&#1082; &#1089;&#1072;&#1081;&#1090;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Site information</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Site information</span>\"\n                    \"<span class=sf-dump-key>Site logo</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Site logo</span>\"\n                    \"<span class=sf-dump-key>Slider Image :number</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Slider Image :number</span>\"\n                    \"<span class=sf-dump-key>Slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Slug</span>\"\n                    \"<span class=sf-dump-key>Social</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Social</span>\"\n                    \"<span class=sf-dump-key>Social Links</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Social Links</span>\"\n                    \"<span class=sf-dump-key>Social Sharing</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Social Sharing</span>\"\n                    \"<span class=sf-dump-key>Social links</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Social links</span>\"\n                    \"<span class=sf-dump-key>Social sharing buttons</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Social sharing buttons</span>\"\n                    \"<span class=sf-dump-key>Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.</span>\" => \"<span class=sf-dump-str title=\"157 characters\">Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.</span>\"\n                    \"<span class=sf-dump-key>Something went wrong.</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Something went wrong.</span>\"\n                    \"<span class=sf-dump-key>Sorry, we are doing some maintenance. Please check back soon.</span>\" => \"<span class=sf-dump-str title=\"74 characters\">&#1048;&#1079;&#1074;&#1080;&#1085;&#1080;&#1090;&#1077;, &#1084;&#1099; &#1087;&#1088;&#1086;&#1074;&#1086;&#1076;&#1080;&#1084; &#1090;&#1077;&#1093;&#1085;&#1080;&#1095;&#1077;&#1089;&#1082;&#1086;&#1077; &#1086;&#1073;&#1089;&#1083;&#1091;&#1078;&#1080;&#1074;&#1072;&#1085;&#1080;&#1077;. &#1055;&#1086;&#1078;&#1072;&#1083;&#1091;&#1081;&#1089;&#1090;&#1072;, &#1079;&#1072;&#1081;&#1076;&#1080;&#1090;&#1077; &#1087;&#1086;&#1079;&#1078;&#1077;.</span>\"\n                    \"<span class=sf-dump-key>Sort by</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1057;&#1086;&#1088;&#1090;&#1080;&#1088;&#1086;&#1074;&#1072;&#1090;&#1100; &#1087;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Speak</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Speak</span>\"\n                    \"<span class=sf-dump-key>Square</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1055;&#1083;&#1086;&#1097;&#1072;&#1076;&#1100;</span>\"\n                    \"<span class=sf-dump-key>Square Range</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Square Range</span>\"\n                    \"<span class=sf-dump-key>Square from</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Square from</span>\"\n                    \"<span class=sf-dump-key>Square to</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Square to</span>\"\n                    \"<span class=sf-dump-key>Square:</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#1055;&#1083;&#1086;&#1097;&#1072;&#1076;&#1100;:</span>\"\n                    \"<span class=sf-dump-key>Standard</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Standard</span>\"\n                    \"<span class=sf-dump-key>State</span>\" => \"<span class=sf-dump-str title=\"5 characters\">State</span>\"\n                    \"<span class=sf-dump-key>States</span>\" => \"<span class=sf-dump-str title=\"6 characters\">States</span>\"\n                    \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1057;&#1090;&#1072;&#1090;&#1091;&#1089;</span>\"\n                    \"<span class=sf-dump-key>Stop on the last slide</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Stop on the last slide</span>\"\n                    \"<span class=sf-dump-key>Store ID</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Store ID</span>\"\n                    \"<span class=sf-dump-key>Store Password (API/Secret key)</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Store Password (API/Secret key)</span>\"\n                    \"<span class=sf-dump-key>Style</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Style</span>\"\n                    \"<span class=sf-dump-key>Style :number</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Style :number</span>\"\n                    \"<span class=sf-dump-key>Styles</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Styles</span>\"\n                    \"<span class=sf-dump-key>Subject</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                    \"<span class=sf-dump-key>Submit Property</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Submit Property</span>\"\n                    \"<span class=sf-dump-key>Submit review</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Submit review</span>\"\n                    \"<span class=sf-dump-key>Subscribe</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Subscribe</span>\"\n                    \"<span class=sf-dump-key>Subscribe to newsletter successfully!</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Subscribe to newsletter successfully!</span>\"\n                    \"<span class=sf-dump-key>Subtitle</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Subtitle</span>\"\n                    \"<span class=sf-dump-key>Support native audio</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Support native audio</span>\"\n                    \"<span class=sf-dump-key>Support native video, YouTube, Vimeo, TikTok, X (Twitter)</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Support native video, YouTube, Vimeo, TikTok, X (Twitter)</span>\"\n                    \"<span class=sf-dump-key>Tab #:number</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tab #:number</span>\"\n                    \"<span class=sf-dump-key>Tablet Image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Tablet Image</span>\"\n                    \"<span class=sf-dump-key>Tabs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tabs</span>\"\n                    \"<span class=sf-dump-key>Tag:</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1058;&#1077;&#1075;:</span>\"\n                    \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">&#1058;&#1077;&#1075;&#1080;</span>\"\n                    \"<span class=sf-dump-key>Take me home</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Take me home</span>\"\n                    \"<span class=sf-dump-key>Telegram</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Telegram</span>\"\n                    \"<span class=sf-dump-key>Temporarily down for maintenance</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Temporarily down for maintenance</span>\"\n                    \"<span class=sf-dump-key>Term and Privacy Policy URL</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Term and Privacy Policy URL</span>\"\n                    \"<span class=sf-dump-key>Terms and Privacy Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Terms and Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>Testimonials</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Testimonials</span>\"\n                    \"<span class=sf-dump-key>Text</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1058;&#1077;&#1082;&#1089;&#1090;</span>\"\n                    \"<span class=sf-dump-key>The .env file is not writable.</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The .env file is not writable.</span>\"\n                    \"<span class=sf-dump-key>The Most Recent Estate</span>\" => \"<span class=sf-dump-str title=\"22 characters\">The Most Recent Estate</span>\"\n                    \"<span class=sf-dump-key>The company address of invoice</span>\" => \"<span class=sf-dump-str title=\"30 characters\">The company address of invoice</span>\"\n                    \"<span class=sf-dump-key>The company email of invoice</span>\" => \"<span class=sf-dump-str title=\"28 characters\">The company email of invoice</span>\"\n                    \"<span class=sf-dump-key>The company name of invoice</span>\" => \"<span class=sf-dump-str title=\"27 characters\">The company name of invoice</span>\"\n                    \"<span class=sf-dump-key>The company phone number of invoice</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The company phone number of invoice</span>\"\n                    \"<span class=sf-dump-key>The debug mode has been disabled successfully.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">The debug mode has been disabled successfully.</span>\"\n                    \"<span class=sf-dump-key>The debug mode is already disabled.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The debug mode is already disabled.</span>\"\n                    \"<span class=sf-dump-key>The font family of invoice template</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The font family of invoice template</span>\"\n                    \"<span class=sf-dump-key>The font size in pixels (px). Default is :default</span>\" => \"<span class=sf-dump-str title=\"49 characters\">The font size in pixels (px). Default is :default</span>\"\n                    \"<span class=sf-dump-key>The given email address has not been confirmed. &lt;a href=&quot;:resend_link&quot;&gt;Resend confirmation link.&lt;/a&gt;</span>\" => \"<span class=sf-dump-str title=\"100 characters\">The given email address has not been confirmed. &lt;a href=&quot;:resend_link&quot;&gt;Resend confirmation link.&lt;/a&gt;</span>\"\n                    \"<span class=sf-dump-key>The page you are looking for could not be found.</span>\" => \"<span class=sf-dump-str title=\"48 characters\">The page you are looking for could not be found.</span>\"\n                    \"<span class=sf-dump-key>The selected :attribute is invalid.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">The selected :attribute is invalid.</span>\"\n                    \"<span class=sf-dump-key>The system is up-to-date. There are no new versions to update!</span>\" => \"<span class=sf-dump-str title=\"62 characters\">The system is up-to-date. There are no new versions to update!</span>\"\n                    \"<span class=sf-dump-key>Theme built-in</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Theme built-in</span>\"\n                    \"<span class=sf-dump-key>Theme options</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Theme options</span>\"\n                    \"<span class=sf-dump-key>Theme options for Real Estate</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Theme options for Real Estate</span>\"\n                    \"<span class=sf-dump-key>There is no data to display!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">There is no data to display!</span>\"\n                    \"<span class=sf-dump-key>This coupon is invalid!</span>\" => \"<span class=sf-dump-str title=\"23 characters\">This coupon is invalid!</span>\"\n                    \"<span class=sf-dump-key>This coupon is not used yet!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">This coupon is not used yet!</span>\"\n                    \"<span class=sf-dump-key>This credential is invalid Google Analytics credentials.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">This credential is invalid Google Analytics credentials.</span>\"\n                    \"<span class=sf-dump-key>This feature is temporary disabled in demo mode. Please use another login option. Such as Google.</span>\" => \"<span class=sf-dump-str title=\"97 characters\">This feature is temporary disabled in demo mode. Please use another login option. Such as Google.</span>\"\n                    \"<span class=sf-dump-key>This file is not a valid JSON file.</span>\" => \"<span class=sf-dump-str title=\"35 characters\">This file is not a valid JSON file.</span>\"\n                    \"<span class=sf-dump-key>This icon will be used in the frontend for featured amenities.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">This icon will be used in the frontend for featured amenities.</span>\"\n                    \"<span class=sf-dump-key>This image will be used as placeholder for lazy load images.</span>\" => \"<span class=sf-dump-str title=\"60 characters\">This image will be used as placeholder for lazy load images.</span>\"\n                    \"<span class=sf-dump-key>This will replace the icon if it set.</span>\" => \"<span class=sf-dump-str title=\"37 characters\">This will replace the icon if it set.</span>\"\n                    \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1047;&#1072;&#1075;&#1086;&#1083;&#1086;&#1074;&#1086;&#1082;</span>\"\n                    \"<span class=sf-dump-key>To</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1054;&#1090;</span>\"\n                    \"<span class=sf-dump-key>To show chat box on that website, please go to :link and add :domain to whitelist domains!</span>\" => \"<span class=sf-dump-str title=\"90 characters\">To show chat box on that website, please go to :link and add :domain to whitelist domains!</span>\"\n                    \"<span class=sf-dump-key>Top Footer Sidebar</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Top Footer Sidebar</span>\"\n                    \"<span class=sf-dump-key>Top header background color</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Top header background color</span>\"\n                    \"<span class=sf-dump-key>Top header text color</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Top header text color</span>\"\n                    \"<span class=sf-dump-key>Top section of the footer for logo and social links.</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Top section of the footer for logo and social links.</span>\"\n                    \"<span class=sf-dump-key>Transaction is already successfully completed!</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Transaction is already successfully completed!</span>\"\n                    \"<span class=sf-dump-key>Transaction is successfully completed!</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Transaction is successfully completed!</span>\"\n                    \"<span class=sf-dump-key>Transactions</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Transactions</span>\"\n                    \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1058;&#1080;&#1087;</span>\"\n                    \"<span class=sf-dump-key>URL</span>\" => \"<span class=sf-dump-str title=\"3 characters\">URL</span>\"\n                    \"<span class=sf-dump-key>Unable to set debug mode. No APP_DEBUG variable was found in the .env file.</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Unable to set debug mode. No APP_DEBUG variable was found in the .env file.</span>\"\n                    \"<span class=sf-dump-key>Unknown</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Unknown</span>\"\n                    \"<span class=sf-dump-key>Unsubscribe to newsletter successfully</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Unsubscribe to newsletter successfully</span>\"\n                    \"<span class=sf-dump-key>Update :name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1054;&#1073;&#1085;&#1086;&#1074;&#1080;&#1090;&#1100; :name</span>\"\n                    \"<span class=sf-dump-key>Upload Service Account JSON File</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Upload Service Account JSON File</span>\"\n                    \"<span class=sf-dump-key>Use Modal for Login/Register</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Use Modal for Login/Register</span>\"\n                    \"<span class=sf-dump-key>Use location in search box as dropdown instead of input auto-complete</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Use location in search box as dropdown instead of input auto-complete</span>\"\n                    \"<span class=sf-dump-key>Use the YouTube video link to be able to watch the video directly on the website.</span>\" => \"<span class=sf-dump-str title=\"81 characters\">Use the YouTube video link to be able to watch the video directly on the website.</span>\"\n                    \"<span class=sf-dump-key>Username</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Username</span>\"\n                    \"<span class=sf-dump-key>Validation Fail!</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Validation Fail!</span>\"\n                    \"<span class=sf-dump-key>Variables</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Variables</span>\"\n                    \"<span class=sf-dump-key>Video</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1042;&#1080;&#1076;&#1077;&#1086;</span>\"\n                    \"<span class=sf-dump-key>Video URL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Video URL</span>\"\n                    \"<span class=sf-dump-key>Video thumbnail</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Video thumbnail</span>\"\n                    \"<span class=sf-dump-key>View</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1055;&#1088;&#1086;&#1089;&#1084;&#1086;&#1090;&#1088;&#1086;&#1074;</span>\"\n                    \"<span class=sf-dump-key>View All</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1055;&#1086;&#1082;&#1072;&#1079;&#1072;&#1090;&#1100; &#1074;&#1089;&#1077;</span>\"\n                    \"<span class=sf-dump-key>View All Photos (:count)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1042;&#1089;&#1077; &#1092;&#1086;&#1090;&#1086; (:count)</span>\"\n                    \"<span class=sf-dump-key>View your ads.txt here: :url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">View your ads.txt here: :url</span>\"\n                    \"<span class=sf-dump-key>We have sent you an email to verify your email. Please check and confirm your email address!</span>\" => \"<span class=sf-dump-str title=\"92 characters\">We have sent you an email to verify your email. Please check and confirm your email address!</span>\"\n                    \"<span class=sf-dump-key>We regularly recruit at many positions. See related jobs here</span>\" => \"<span class=sf-dump-str title=\"61 characters\">We regularly recruit at many positions. See related jobs here</span>\"\n                    \"<span class=sf-dump-key>We sent you another confirmation email. You should receive it shortly.</span>\" => \"<span class=sf-dump-str title=\"70 characters\">We sent you another confirmation email. You should receive it shortly.</span>\"\n                    \"<span class=sf-dump-key>WhatsApp</span>\" => \"<span class=sf-dump-str title=\"8 characters\">WhatsApp</span>\"\n                    \"<span class=sf-dump-key>What&#8217;s nearby?</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1048;&#1085;&#1092;&#1088;&#1072;&#1089;&#1090;&#1088;&#1091;&#1082;&#1090;&#1091;&#1088;&#1072;</span>\"\n                    \"<span class=sf-dump-key>When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.</span>\" => \"<span class=sf-dump-str title=\"142 characters\">When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.</span>\"\n                    \"<span class=sf-dump-key>When the login/register button is clicked, a popup will appear with the login/register form instead of redirecting users to another page.</span>\" => \"<span class=sf-dump-str title=\"137 characters\">When the login/register button is clicked, a popup will appear with the login/register form instead of redirecting users to another page.</span>\"\n                    \"<span class=sf-dump-key>Widget display blog categories</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Widget display blog categories</span>\"\n                    \"<span class=sf-dump-key>Widget display site information</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Widget display site information</span>\"\n                    \"<span class=sf-dump-key>Widget display site logo</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Widget display site logo</span>\"\n                    \"<span class=sf-dump-key>Widget display social links network</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Widget display social links network</span>\"\n                    \"<span class=sf-dump-key>Width</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1044;&#1083;&#1080;&#1085;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Wishlist</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1048;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Without map</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1041;&#1077;&#1079; &#1082;&#1072;&#1088;&#1090;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Write A Review</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1044;&#1086;&#1073;&#1072;&#1074;&#1080;&#1090;&#1100; &#1086;&#1090;&#1079;&#1099;&#1074;</span>\"\n                    \"<span class=sf-dump-key>Write a review</span>\" => \"<span class=sf-dump-str title=\"14 characters\">&#1044;&#1086;&#1073;&#1072;&#1074;&#1080;&#1090;&#1100; &#1086;&#1090;&#1079;&#1099;&#1074;</span>\"\n                    \"<span class=sf-dump-key>Write comment</span>\" => \"<span class=sf-dump-str title=\"20 characters\">&#1044;&#1086;&#1073;&#1072;&#1074;&#1080;&#1090;&#1100; &#1082;&#1086;&#1084;&#1084;&#1077;&#1085;&#1090;&#1072;&#1088;&#1080;&#1081;</span>\"\n                    \"<span class=sf-dump-key>Write your message here</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1053;&#1072;&#1087;&#1080;&#1096;&#1080;&#1090;&#1077; &#1074;&#1072;&#1096;&#1077; &#1089;&#1086;&#1086;&#1073;&#1097;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>X (Twitter)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">X (Twitter)</span>\"\n                    \"<span class=sf-dump-key>Yes</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1044;&#1072;</span>\"\n                    \"<span class=sf-dump-key>Yes, turn off</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Yes, turn off</span>\"\n                    \"<span class=sf-dump-key>You can change logo in Appearance &#8594; Theme Options &#8594; Logo.</span>\" => \"<span class=sf-dump-str title=\"57 characters\">You can change logo in Appearance &#8594; Theme Options &#8594; Logo.</span>\"\n                    \"<span class=sf-dump-key>You can create your app in :link</span>\" => \"<span class=sf-dump-str title=\"32 characters\">You can create your app in :link</span>\"\n                    \"<span class=sf-dump-key>You can get fan page ID using this site :link</span>\" => \"<span class=sf-dump-str title=\"45 characters\">You can get fan page ID using this site :link</span>\"\n                    \"<span class=sf-dump-key>You can use HTML tags. Example: &amp;lt;a href=&amp;quot;tel:0123456789&amp;quot;&amp;gt;0123456789&amp;lt;/a&amp;gt; or &amp;lt;br&amp;gt; for line break</span>\" => \"<span class=sf-dump-str title=\"122 characters\">You can use HTML tags. Example: &amp;lt;a href=&amp;quot;tel:0123456789&amp;quot;&amp;gt;0123456789&amp;lt;/a&amp;gt; or &amp;lt;br&amp;gt; for line break</span>\"\n                    \"<span class=sf-dump-key>You don&#039;t have enough credit to renew this property!</span>\" => \"<span class=sf-dump-str title=\"52 characters\">You don&#039;t have enough credit to renew this property!</span>\"\n                    \"<span class=sf-dump-key>You have already submitted a review.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">You have already submitted a review.</span>\"\n                    \"<span class=sf-dump-key>You have created a payment #:charge_id via :channel :time : :amount</span>\" => \"<span class=sf-dump-str title=\"67 characters\">You have created a payment #:charge_id via :channel :time : :amount</span>\"\n                    \"<span class=sf-dump-key>You have not added any properties or projects to your wishlist.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&#1055;&#1086;&#1082;&#1072; &#1085;&#1080;&#1095;&#1077;&#1075;&#1086; &#1085;&#1077; &#1076;&#1086;&#1073;&#1072;&#1074;&#1083;&#1077;&#1085;&#1086; &#1074; &#1080;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>You have purchased :credits credit(s)</span>\" => \"<span class=sf-dump-str title=\"37 characters\">You have purchased :credits credit(s)</span>\"\n                    \"<span class=sf-dump-key>You need to login to write review.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">You need to login to write review.</span>\"\n                    \"<span class=sf-dump-key>You successfully confirmed your email address.</span>\" => \"<span class=sf-dump-str title=\"46 characters\">You successfully confirmed your email address.</span>\"\n                    \"<span class=sf-dump-key>You will be redirected to :name to complete the payment.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">You will be redirected to :name to complete the payment.</span>\"\n                    \"<span class=sf-dump-key>YouTube</span>\" => \"<span class=sf-dump-str title=\"7 characters\">YouTube</span>\"\n                    \"<span class=sf-dump-key>YouTube URL</span>\" => \"<span class=sf-dump-str title=\"11 characters\">YouTube URL</span>\"\n                    \"<span class=sf-dump-key>YouTube video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">YouTube &#1074;&#1080;&#1076;&#1077;&#1086;</span>\"\n                    \"<span class=sf-dump-key>YouTube, Vimeo, TikTok, ...</span>\" => \"<span class=sf-dump-str title=\"27 characters\">YouTube, Vimeo, TikTok, ...</span>\"\n                    \"<span class=sf-dump-key>Your Address</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1042;&#1072;&#1096; &#1072;&#1076;&#1088;&#1077;&#1089;</span>\"\n                    \"<span class=sf-dump-key>Your Email</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1042;&#1072;&#1096; &#1080;&#1084;&#1077;&#1081;&#1083;</span>\"\n                    \"<span class=sf-dump-key>Your Favorite Projects</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#1048;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1099;&#1077; &#1087;&#1088;&#1086;&#1077;&#1082;&#1090;&#1099;</span>\"\n                    \"<span class=sf-dump-key>Your Favorite Properties</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1048;&#1079;&#1073;&#1088;&#1072;&#1085;&#1085;&#1086;&#1077;</span>\"\n                    \"<span class=sf-dump-key>Your Google Adsense ads.txt</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Your Google Adsense ads.txt</span>\"\n                    \"<span class=sf-dump-key>Your Name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1048;&#1084;&#1103;</span>\"\n                    \"<span class=sf-dump-key>Your Phone</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1058;&#1077;&#1083;&#1077;&#1092;&#1086;&#1085;</span>\"\n                    \"<span class=sf-dump-key>Your asset files have been published successfully.</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Your asset files have been published successfully.</span>\"\n                    \"<span class=sf-dump-key>Your email does not exist in the system or you have unsubscribed already!</span>\" => \"<span class=sf-dump-str title=\"73 characters\">Your email does not exist in the system or you have unsubscribed already!</span>\"\n                    \"<span class=sf-dump-key>Your email is in blacklist. Please use another email address.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Your email is in blacklist. Please use another email address.</span>\"\n                    \"<span class=sf-dump-key>Your file is not found. Please try uploading again.</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Your file is not found. Please try uploading again.</span>\"\n                    \"<span class=sf-dump-key>Your message contains blacklist words: &quot;:words&quot;.</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Your message contains blacklist words: &quot;:words&quot;.</span>\"\n                    \"<span class=sf-dump-key>Your order</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Your order</span>\"\n                    \"<span class=sf-dump-key>Your personal data will be used to support your experience throughout this website, to manage access to your account.</span>\" => \"<span class=sf-dump-str title=\"117 characters\">Your personal data will be used to support your experience throughout this website, to manage access to your account.</span>\"\n                    \"<span class=sf-dump-key>Your review has been submitted!</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Your review has been submitted!</span>\"\n                    \"<span class=sf-dump-key>Your system has been cleaned up successfully.</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Your system has been cleaned up successfully.</span>\"\n                    \"<span class=sf-dump-key>Z-A</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1071;-&#1040;</span>\"\n                    \"<span class=sf-dump-key>bathroom</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#1074;&#1072;&#1085;&#1085;&#1099;&#1093;</span>\"\n                    \"<span class=sf-dump-key>bedroom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1089;&#1087;&#1072;&#1083;&#1077;&#1085;&#1100;</span>\"\n                    \"<span class=sf-dump-key>billion</span>\" => \"<span class=sf-dump-str title=\"7 characters\">billion</span>\"\n                    \"<span class=sf-dump-key>block</span>\" => \"<span class=sf-dump-str title=\"5 characters\">block</span>\"\n                    \"<span class=sf-dump-key>blocks</span>\" => \"<span class=sf-dump-str title=\"6 characters\">blocks</span>\"\n                    \"<span class=sf-dump-key>floor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1091;&#1088;&#1086;&#1074;&#1085;&#1077;&#1081;</span>\"\n                    \"<span class=sf-dump-key>floors</span>\" => \"<span class=sf-dump-str title=\"6 characters\">floors</span>\"\n                    \"<span class=sf-dump-key>free</span>\" => \"<span class=sf-dump-str title=\"4 characters\">free</span>\"\n                    \"<span class=sf-dump-key>from</span>\" => \"<span class=sf-dump-str title=\"4 characters\">from</span>\"\n                    \"<span class=sf-dump-key>ft2</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ft2</span>\"\n                    \"<span class=sf-dump-key>here</span>\" => \"<span class=sf-dump-str title=\"4 characters\">here</span>\"\n                    \"<span class=sf-dump-key>high to low</span>\" => \"<span class=sf-dump-str title=\"11 characters\">high to low</span>\"\n                    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n                    \"<span class=sf-dump-key>listing</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1086;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1077;</span>\"\n                    \"<span class=sf-dump-key>listings</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1086;&#1073;&#1100;&#1103;&#1074;&#1083;&#1077;&#1085;&#1080;&#1081;</span>\"\n                    \"<span class=sf-dump-key>low to high</span>\" => \"<span class=sf-dump-str title=\"11 characters\">low to high</span>\"\n                    \"<span class=sf-dump-key>million</span>\" => \"<span class=sf-dump-str title=\"7 characters\">million</span>\"\n                    \"<span class=sf-dump-key>m&#178;</span>\" => \"<span class=sf-dump-str title=\"2 characters\">m&#178;</span>\"\n                    \"<span class=sf-dump-key>of use of the site</span>\" => \"<span class=sf-dump-str title=\"18 characters\">of use of the site</span>\"\n                    \"<span class=sf-dump-key>per post</span>\" => \"<span class=sf-dump-str title=\"8 characters\">per post</span>\"\n                    \"<span class=sf-dump-key>post(s)</span>\" => \"<span class=sf-dump-str title=\"7 characters\">post(s)</span>\"\n                    \"<span class=sf-dump-key>posts</span>\" => \"<span class=sf-dump-str title=\"5 characters\">posts</span>\"\n                    \"<span class=sf-dump-key>property</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1085;&#1077;&#1076;&#1074;&#1080;&#1078;&#1080;&#1084;&#1086;&#1089;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>room</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1082;&#1086;&#1084;&#1085;&#1072;&#1090;&#1099;</span>\"\n                    \"<span class=sf-dump-key>rooms</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#1082;&#1086;&#1084;&#1085;&#1072;&#1090;&#1099;</span>\"\n                    \"<span class=sf-dump-key>save</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1089;&#1086;&#1093;&#1088;&#1072;&#1085;&#1080;&#1090;&#1100;</span>\"\n                    \"<span class=sf-dump-key>save :percentage %</span>\" => \"<span class=sf-dump-str title=\"23 characters\">&#1089;&#1086;&#1093;&#1088;&#1072;&#1085;&#1080;&#1090;&#1100; :percentage %</span>\"\n                    \"<span class=sf-dump-key>the terms and conditions</span>\" => \"<span class=sf-dump-str title=\"24 characters\">the terms and conditions</span>\"\n                    \"<span class=sf-dump-key>to</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1086;&#1090;</span>\"\n                    \"<span class=sf-dump-key>total</span>\" => \"<span class=sf-dump-str title=\"5 characters\">&#1074;&#1089;&#1077;&#1075;&#1086;</span>\"\n                    \"<span class=sf-dump-key>via</span>\" => \"<span class=sf-dump-str title=\"3 characters\">via</span>\"\n                    \"<span class=sf-dump-key>views</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#1087;&#1088;&#1086;&#1089;&#1084;&#1086;&#1090;&#1088;&#1086;&#1074;</span>\"\n                    \"<span class=sf-dump-key>yd2</span>\" => \"<span class=sf-dump-str title=\"3 characters\">yd2</span>\"\n                    \"<span class=sf-dump-key>&#1084;&#178;</span>\" => \"<span class=sf-dump-str title=\"2 characters\">&#1084;&#178;</span>\"\n                  </samp>]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:870</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>-- None --</span>\" => \"<span class=sf-dump-str title=\"10 characters\">-- None --</span>\"\n                    \"<span class=sf-dump-key>-- Select --</span>\" => \"<span class=sf-dump-str title=\"12 characters\">-- Select --</span>\"\n                    \"<span class=sf-dump-key>1 Bathroom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1 Bathroom</span>\"\n                    \"<span class=sf-dump-key>1 Bedroom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1 Bedroom</span>\"\n                    \"<span class=sf-dump-key>1 Block</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 Block</span>\"\n                    \"<span class=sf-dump-key>1 Floor</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 Floor</span>\"\n                    \"<span class=sf-dump-key>1 Property</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1 Property</span>\"\n                    \"<span class=sf-dump-key>1 Review</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 Review</span>\"\n                    \"<span class=sf-dump-key>1 Views</span>\" => \"<span class=sf-dump-str title=\"7 characters\">1 Views</span>\"\n                    \"<span class=sf-dump-key>1 bathroom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1 bathroom</span>\"\n                    \"<span class=sf-dump-key>1 bedroom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">1 bedroom</span>\"\n                    \"<span class=sf-dump-key>1 credit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">1 credit</span>\"\n                    \"<span class=sf-dump-key>1 post</span>\" => \"<span class=sf-dump-str title=\"6 characters\">1 post</span>\"\n                    \"<span class=sf-dump-key>bedroom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bedroom</span>\"\n                    \"<span class=sf-dump-key>bathroom</span>\" => \"<span class=sf-dump-str title=\"8 characters\">bathroom</span>\"\n                    \"<span class=sf-dump-key>block</span>\" => \"<span class=sf-dump-str title=\"5 characters\">block</span>\"\n                    \"<span class=sf-dump-key>floor</span>\" => \"<span class=sf-dump-str title=\"5 characters\">floor</span>\"\n                    \"<span class=sf-dump-key>property</span>\" => \"<span class=sf-dump-str title=\"8 characters\">property</span>\"\n                    \"<span class=sf-dump-key>404 - Page Not found</span>\" => \"<span class=sf-dump-str title=\"20 characters\">404 - Page Not found</span>\"\n                    \"<span class=sf-dump-key>404 Page Not Found</span>\" => \"<span class=sf-dump-str title=\"18 characters\">404 Page Not Found</span>\"\n                    \"<span class=sf-dump-key>Found</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Found</span>\"\n                    \"<span class=sf-dump-key>5+ blocks</span>\" => \"<span class=sf-dump-str title=\"9 characters\">5+ blocks</span>\"\n                    \"<span class=sf-dump-key>5+ floors</span>\" => \"<span class=sf-dump-str title=\"9 characters\">5+ floors</span>\"\n                    \"<span class=sf-dump-key>5+ rooms</span>\" => \"<span class=sf-dump-str title=\"8 characters\">5+ rooms</span>\"\n                    \"<span class=sf-dump-key>500 Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"25 characters\">500 Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>503 Service Unavailable</span>\" => \"<span class=sf-dump-str title=\"23 characters\">503 Service Unavailable</span>\"\n                    \"<span class=sf-dump-key>:avg out of 5</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:avg out of 5</span>\"\n                    \"<span class=sf-dump-key>:count Properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:count Properties</span>\"\n                    \"<span class=sf-dump-key>:count Review(s)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:count Review(s)</span>\"\n                    \"<span class=sf-dump-key>:count bathrooms</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:count bathrooms</span>\"\n                    \"<span class=sf-dump-key>:count bedrooms</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count bedrooms</span>\"\n                    \"<span class=sf-dump-key>:count credits</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:count credits</span>\"\n                    \"<span class=sf-dump-key>:count properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:count properties</span>\"\n                    \"<span class=sf-dump-key>:count property</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:count property</span>\"\n                    \"<span class=sf-dump-key>:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">:name doesn&#039;t support :currency. List of currencies supported by :name: :currencies.</span>\"\n                    \"<span class=sf-dump-key>:name feed</span>\" => \"<span class=sf-dump-str title=\"10 characters\">:name feed</span>\"\n                    \"<span class=sf-dump-key>:name font family</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:name font family</span>\"\n                    \"<span class=sf-dump-key>:name font size</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:name font size</span>\"\n                    \"<span class=sf-dump-key>:number Bathrooms</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:number Bathrooms</span>\"\n                    \"<span class=sf-dump-key>:number Bedrooms</span>\" => \"<span class=sf-dump-str title=\"16 characters\">:number Bedrooms</span>\"\n                    \"<span class=sf-dump-key>:number Blocks</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Blocks</span>\"\n                    \"<span class=sf-dump-key>:number Column</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Column</span>\"\n                    \"<span class=sf-dump-key>:number Columns</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number Columns</span>\"\n                    \"<span class=sf-dump-key>:number Floors</span>\" => \"<span class=sf-dump-str title=\"14 characters\">:number Floors</span>\"\n                    \"<span class=sf-dump-key>:number Reviews</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number Reviews</span>\"\n                    \"<span class=sf-dump-key>:number Views</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:number Views</span>\"\n                    \"<span class=sf-dump-key>:number posts</span>\" => \"<span class=sf-dump-str title=\"13 characters\">:number posts</span>\"\n                    \"<span class=sf-dump-key>:number+ Bathrooms</span>\" => \"<span class=sf-dump-str title=\"18 characters\">:number+ Bathrooms</span>\"\n                    \"<span class=sf-dump-key>:number+ Bedrooms</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:number+ Bedrooms</span>\"\n                    \"<span class=sf-dump-key>:number+ Blocks</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number+ Blocks</span>\"\n                    \"<span class=sf-dump-key>:number+ Floors</span>\" => \"<span class=sf-dump-str title=\"15 characters\">:number+ Floors</span>\"\n                    \"<span class=sf-dump-key>:price / per post</span>\" => \"<span class=sf-dump-str title=\"17 characters\">:price / per post</span>\"\n                    \"<span class=sf-dump-key>:price Total :percentage_sale</span>\" => \"<span class=sf-dump-str title=\"29 characters\">:price Total :percentage_sale</span>\"\n                    \"<span class=sf-dump-key>A content tab to display information with a title.</span>\" => \"<span class=sf-dump-str title=\"50 characters\">A content tab to display information with a title.</span>\"\n                    \"<span class=sf-dump-key>A new version (:version / released on :date) is available to update!</span>\" => \"<span class=sf-dump-str title=\"68 characters\">A new version (:version / released on :date) is available to update!</span>\"\n                    \"<span class=sf-dump-key>A-Z</span>\" => \"<span class=sf-dump-str title=\"3 characters\">A-Z</span>\"\n                    \"<span class=sf-dump-key>API Key</span>\" => \"<span class=sf-dump-str title=\"7 characters\">API Key</span>\"\n                    \"<span class=sf-dump-key>About</span>\" => \"<span class=sf-dump-str title=\"5 characters\">About</span>\"\n                    \"<span class=sf-dump-key>About Agent</span>\" => \"<span class=sf-dump-str title=\"11 characters\">About Agent</span>\"\n                    \"<span class=sf-dump-key>About Us</span>\" => \"<span class=sf-dump-str title=\"8 characters\">About Us</span>\"\n                    \"<span class=sf-dump-key>Accept and install</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Accept and install</span>\"\n                    \"<span class=sf-dump-key>Action</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Action</span>\"\n                    \"<span class=sf-dump-key>Add</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Add</span>\"\n                    \"<span class=sf-dump-key>Add listing</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Add listing</span>\"\n                    \"<span class=sf-dump-key>Edit listing</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Edit listing</span>\"\n                    \"<span class=sf-dump-key>Edit profile</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Edit profile</span>\"\n                    \"<span class=sf-dump-key>Featured Amenity</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Featured Amenity</span>\"\n                    \"<span class=sf-dump-key>This icon will be used in the frontend for featured amenities.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">This icon will be used in the frontend for featured amenities.</span>\"\n                    \"<span class=sf-dump-key>Add Google Maps iframe</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Add Google Maps iframe</span>\"\n                    \"<span class=sf-dump-key>Add YouTube video</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Add YouTube video</span>\"\n                    \"<span class=sf-dump-key>Add a custom menu to your widget area.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Add a custom menu to your widget area.</span>\"\n                    \"<span class=sf-dump-key>Add credit to account</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Add credit to account</span>\"\n                    \"<span class=sf-dump-key>Add custom HTML content</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Add custom HTML content</span>\"\n                    \"<span class=sf-dump-key>Add new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Add new</span>\"\n                    \"<span class=sf-dump-key>Add widgets here to appear in the sidebar of your blog pages.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Add widgets here to appear in the sidebar of your blog pages.</span>\"\n                    \"<span class=sf-dump-key>Added &quot;:name&quot; to wishlist successfully!</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Added &quot;:name&quot; to wishlist successfully!</span>\"\n                    \"<span class=sf-dump-key>Added :credits credit(s) by admin &quot;:user&quot;</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Added :credits credit(s) by admin &quot;:user&quot;</span>\"\n                    \"<span class=sf-dump-key>Address</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Address</span>\"\n                    \"<span class=sf-dump-key>Ads</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Ads</span>\"\n                    \"<span class=sf-dump-key>Advanced</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Advanced</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have API key</span>\" => \"<span class=sf-dump-str title=\"50 characters\">After registration at :name, you will have API key</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Client ID, Client Secret</span>\" => \"<span class=sf-dump-str title=\"67 characters\">After registration at :name, you will have Client ID, Client Secret</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Public &amp; Secret keys</span>\" => \"<span class=sf-dump-str title=\"63 characters\">After registration at :name, you will have Public &amp; Secret keys</span>\"\n                    \"<span class=sf-dump-key>After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\" => \"<span class=sf-dump-str title=\"87 characters\">After registration at :name, you will have Store ID and Store Password (API/Secret key)</span>\"\n                    \"<span class=sf-dump-key>Agent</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Agent</span>\"\n                    \"<span class=sf-dump-key>Owner</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Owner</span>\"\n                    \"<span class=sf-dump-key>Agents</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Agents</span>\"\n                    \"<span class=sf-dump-key>User Speak</span>\" => \"<span class=sf-dump-str title=\"10 characters\">User Speak</span>\"\n                    \"<span class=sf-dump-key>Rent Apartment</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Rent Apartment</span>\"\n                    \"<span class=sf-dump-key>All</span>\" => \"<span class=sf-dump-str title=\"3 characters\">All</span>\"\n                    \"<span class=sf-dump-key>All Pages</span>\" => \"<span class=sf-dump-str title=\"9 characters\">All Pages</span>\"\n                    \"<span class=sf-dump-key>All flats</span>\" => \"<span class=sf-dump-str title=\"9 characters\">All flats</span>\"\n                    \"<span class=sf-dump-key>All squares</span>\" => \"<span class=sf-dump-str title=\"11 characters\">All squares</span>\"\n                    \"<span class=sf-dump-key>Filters</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Filters</span>\"\n                    \"<span class=sf-dump-key>Already have an account?</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Already have an account?</span>\"\n                    \"<span class=sf-dump-key>Have an account?</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Have an account?</span>\"\n                    \"<span class=sf-dump-key>Amenities</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Amenities</span>\"\n                    \"<span class=sf-dump-key>Amenities and features</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Amenities and features</span>\"\n                    \"<span class=sf-dump-key>Amenities:</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Amenities:</span>\"\n                    \"<span class=sf-dump-key>An error occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"39 characters\">An error occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Animation text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Animation text</span>\"\n                    \"<span class=sf-dump-key>Applied coupon &quot;:code&quot; successfully!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Applied coupon &quot;:code&quot; successfully!</span>\"\n                    \"<span class=sf-dump-key>Apply Now</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Apply Now</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to renew this property</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Are you sure you want to renew this property</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to renew this property, it will takes 1 credit from your credits</span>\" => \"<span class=sf-dump-str title=\"86 characters\">Are you sure you want to renew this property, it will takes 1 credit from your credits</span>\"\n                    \"<span class=sf-dump-key>Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\" => \"<span class=sf-dump-str title=\"79 characters\">Are you sure you want to turn off the debug mode? This action cannot be undone.</span>\"\n                    \"<span class=sf-dump-key>Are you sure?</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Are you sure?</span>\"\n                    \"<span class=sf-dump-key>Audio File</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Audio File</span>\"\n                    \"<span class=sf-dump-key>Author</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Author</span>\"\n                    \"<span class=sf-dump-key>Autoplay speed (if autoplay enabled)</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Autoplay speed (if autoplay enabled)</span>\"\n                    \"<span class=sf-dump-key>Back To Home</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Back To Home</span>\"\n                    \"<span class=sf-dump-key>Back to login page</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Back to login page</span>\"\n                    \"<span class=sf-dump-key>Background Image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background Image</span>\"\n                    \"<span class=sf-dump-key>Background color</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background color</span>\"\n                    \"<span class=sf-dump-key>Background image</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Background image</span>\"\n                    \"<span class=sf-dump-key>Banner</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Banner</span>\"\n                    \"<span class=sf-dump-key>Bathrooms</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Bathrooms</span>\"\n                    \"<span class=sf-dump-key>Bathrooms:</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Bathrooms:</span>\"\n                    \"<span class=sf-dump-key>Bedrooms</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Bedrooms</span>\"\n                    \"<span class=sf-dump-key>Bedrooms:</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Bedrooms:</span>\"\n                    \"<span class=sf-dump-key>Bill payment user account information, ex: account.name, account.email, ...</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Bill payment user account information, ex: account.name, account.email, ...</span>\"\n                    \"<span class=sf-dump-key>Blocks</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Blocks</span>\"\n                    \"<span class=sf-dump-key>Blocks:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Blocks:</span>\"\n                    \"<span class=sf-dump-key>Blog Categories</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Blog Categories</span>\"\n                    \"<span class=sf-dump-key>Blog List (after)</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Blog List (after)</span>\"\n                    \"<span class=sf-dump-key>Blog List (before)</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Blog List (before)</span>\"\n                    \"<span class=sf-dump-key>Blog Posts</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Blog Posts</span>\"\n                    \"<span class=sf-dump-key>Blog Search</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Blog Search</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Blog Sidebar</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar (after)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Blog Sidebar (after)</span>\"\n                    \"<span class=sf-dump-key>Blog Sidebar (before)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Blog Sidebar (before)</span>\"\n                    \"<span class=sf-dump-key>Body</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Body</span>\"\n                    \"<span class=sf-dump-key>Bottom Footer Sidebar</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Bottom Footer Sidebar</span>\"\n                    \"<span class=sf-dump-key>Bottom Post Detail Sidebar</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Bottom Post Detail Sidebar</span>\"\n                    \"<span class=sf-dump-key>Bottom footer section for legal notices and credits.</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Bottom footer section for legal notices and credits.</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Breadcrumb</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb background color</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Breadcrumb background color</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb background image</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Breadcrumb background image</span>\"\n                    \"<span class=sf-dump-key>Breadcrumb text color</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Breadcrumb text color</span>\"\n                    \"<span class=sf-dump-key>Business hours</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Business hours</span>\"\n                    \"<span class=sf-dump-key>Button URL</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Button URL</span>\"\n                    \"<span class=sf-dump-key>Button label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Button label</span>\"\n                    \"<span class=sf-dump-key>Buy credits</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Buy credits</span>\"\n                    \"<span class=sf-dump-key>Call To Action</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Call To Action</span>\"\n                    \"<span class=sf-dump-key>Can&#039;t send message on this time, please try again later!</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Can&#039;t send message on this time, please try again later!</span>\"\n                    \"<span class=sf-dump-key>Cancel</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Cancel</span>\"\n                    \"<span class=sf-dump-key>Cannot find this account!</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Cannot find this account!</span>\"\n                    \"<span class=sf-dump-key>Cannot login, no email provided!</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Cannot login, no email provided!</span>\"\n                    \"<span class=sf-dump-key>Captcha</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Captcha</span>\"\n                    \"<span class=sf-dump-key>Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Caption</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Caption</span>\"\n                    \"<span class=sf-dump-key>Careers</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Careers</span>\"\n                    \"<span class=sf-dump-key>Categories</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"\n                    \"<span class=sf-dump-key>Category</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n                    \"<span class=sf-dump-key>Center content</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Center content</span>\"\n                    \"<span class=sf-dump-key>Change copyright</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Change copyright</span>\"\n                    \"<span class=sf-dump-key>Check have enabled the invoice stamp</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Check have enabled the invoice stamp</span>\"\n                    \"<span class=sf-dump-key>Check site is using custom font for invoice or not</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Check site is using custom font for invoice or not</span>\"\n                    \"<span class=sf-dump-key>Checklist</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Checklist</span>\"\n                    \"<span class=sf-dump-key>Checkout</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Checkout</span>\"\n                    \"<span class=sf-dump-key>Checkout error!</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Checkout error!</span>\"\n                    \"<span class=sf-dump-key>Checkout successfully!</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Checkout successfully!</span>\"\n                    \"<span class=sf-dump-key>Choices</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Choices</span>\"\n                    \"<span class=sf-dump-key>Choose The Package</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Choose The Package</span>\"\n                    \"<span class=sf-dump-key>Choose agents</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Choose agents</span>\"\n                    \"<span class=sf-dump-key>Choose categories</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Choose categories</span>\"\n                    \"<span class=sf-dump-key>Choose date format for your front theme.</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Choose date format for your front theme.</span>\"\n                    \"<span class=sf-dump-key>Cities</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Cities</span>\"\n                    \"<span class=sf-dump-key>City</span>\" => \"<span class=sf-dump-str title=\"4 characters\">City</span>\"\n                    \"<span class=sf-dump-key>City, State</span>\" => \"<span class=sf-dump-str title=\"11 characters\">City, State</span>\"\n                    \"<span class=sf-dump-key>Clear</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Clear</span>\"\n                    \"<span class=sf-dump-key>Close</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Close</span>\"\n                    \"<span class=sf-dump-key>Color</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Color</span>\"\n                    \"<span class=sf-dump-key>Coming Soon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Coming Soon</span>\"\n                    \"<span class=sf-dump-key>Company Agent at :company</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Company Agent at :company</span>\"\n                    \"<span class=sf-dump-key>Contact</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Contact</span>\"\n                    \"<span class=sf-dump-key>Contact Agency</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Contact Agency</span>\"\n                    \"<span class=sf-dump-key>Contact agency</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Contact agency</span>\"\n                    \"<span class=sf-dump-key>Content</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Content</span>\"\n                    \"<span class=sf-dump-key>Content Quote</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Content Quote</span>\"\n                    \"<span class=sf-dump-key>Content Tab</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Content Tab</span>\"\n                    \"<span class=sf-dump-key>Content image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Content image</span>\"\n                    \"<span class=sf-dump-key>Continuously</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Continuously</span>\"\n                    \"<span class=sf-dump-key>Copy link</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Copy link</span>\"\n                    \"<span class=sf-dump-key>Copyright</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Copyright</span>\"\n                    \"<span class=sf-dump-key>Copyright on footer of site. Using %Y to display current year.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Copyright on footer of site. Using %Y to display current year.</span>\"\n                    \"<span class=sf-dump-key>Copyright text at the bottom footer.</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Copyright text at the bottom footer.</span>\"\n                    \"<span class=sf-dump-key>Could not download updated file. Please check your license or your internet network.</span>\" => \"<span class=sf-dump-str title=\"84 characters\">Could not download updated file. Please check your license or your internet network.</span>\"\n                    \"<span class=sf-dump-key>Could not update files &amp; database.</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Could not update files &amp; database.</span>\"\n                    \"<span class=sf-dump-key>Countdown time</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Countdown time</span>\"\n                    \"<span class=sf-dump-key>Counters</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Counters</span>\"\n                    \"<span class=sf-dump-key>Coupon code: :code</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Coupon code: :code</span>\"\n                    \"<span class=sf-dump-key>Create engaging call-to-action sections with customizable headings, buttons, and images.</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Create engaging call-to-action sections with customizable headings, buttons, and images.</span>\"\n                    \"<span class=sf-dump-key>Credits</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Credits</span>\"\n                    \"<span class=sf-dump-key>Credits: :count</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Credits: :count</span>\"\n                    \"<span class=sf-dump-key>Currencies</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Currencies</span>\"\n                    \"<span class=sf-dump-key>Custom HTML</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom HTML</span>\"\n                    \"<span class=sf-dump-key>Custom Menu</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Custom Menu</span>\"\n                    \"<span class=sf-dump-key>Customer can buy product and pay directly using Visa, Credit card via :name</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Customer can buy product and pay directly using Visa, Credit card via :name</span>\"\n                    \"<span class=sf-dump-key>Date format</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Date format</span>\"\n                    \"<span class=sf-dump-key>Days</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Days</span>\"\n                    \"<span class=sf-dump-key>Default</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n                    \"<span class=sf-dump-key>Default search type</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default search type</span>\"\n                    \"<span class=sf-dump-key>Delete ads.txt file</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Delete ads.txt file</span>\"\n                    \"<span class=sf-dump-key>Delete property successfully!</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Delete property successfully!</span>\"\n                    \"<span class=sf-dump-key>Description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n                    \"<span class=sf-dump-key>Destination type</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Destination type</span>\"\n                    \"<span class=sf-dump-key>Detail Page (after)</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Detail Page (after)</span>\"\n                    \"<span class=sf-dump-key>Detail Page (before)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Detail Page (before)</span>\"\n                    \"<span class=sf-dump-key>Detail Page Sidebar (after)</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Detail Page Sidebar (after)</span>\"\n                    \"<span class=sf-dump-key>Detail Page Sidebar (before)</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Detail Page Sidebar (before)</span>\"\n                    \"<span class=sf-dump-key>Dismiss</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Dismiss</span>\"\n                    \"<span class=sf-dump-key>Display Newsletter form on sidebar</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Display Newsletter form on sidebar</span>\"\n                    \"<span class=sf-dump-key>Display blog posts</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Display blog posts</span>\"\n                    \"<span class=sf-dump-key>Display on pages</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Display on pages</span>\"\n                    \"<span class=sf-dump-key>Display posts count?</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Display posts count?</span>\"\n                    \"<span class=sf-dump-key>Display recent blog posts</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Display recent blog posts</span>\"\n                    \"<span class=sf-dump-key>Display type</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Display type</span>\"\n                    \"<span class=sf-dump-key>Displays a list of posts related to the current content.</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Displays a list of posts related to the current content.</span>\"\n                    \"<span class=sf-dump-key>Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...</span>\" => \"<span class=sf-dump-str title=\"130 characters\">Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...</span>\"\n                    \"<span class=sf-dump-key>Distance key between facilities</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Distance key between facilities</span>\"\n                    \"<span class=sf-dump-key>Do you want to delete this image?</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Do you want to delete this image?</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t have an account?</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Don&#039;t have an account?</span>\"\n                    \"<span class=sf-dump-key>Don&#039;t show this popup again</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Don&#039;t show this popup again</span>\"\n                    \"<span class=sf-dump-key>Dynamic carousel for featured content with customizable links.</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Dynamic carousel for featured content with customizable links.</span>\"\n                    \"<span class=sf-dump-key>Edit this agent</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Edit this agent</span>\"\n                    \"<span class=sf-dump-key>Edit this project</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Edit this project</span>\"\n                    \"<span class=sf-dump-key>Edit this property</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Edit this property</span>\"\n                    \"<span class=sf-dump-key>Edit this shortcode</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Edit this shortcode</span>\"\n                    \"<span class=sf-dump-key>Edit this widget</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Edit this widget</span>\"\n                    \"<span class=sf-dump-key>Edit</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Edit</span>\"\n                    \"<span class=sf-dump-key>Recover</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Recover</span>\"\n                    \"<span class=sf-dump-key>Rented</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Rented</span>\"\n                    \"<span class=sf-dump-key>Active</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Active</span>\"\n                    \"<span class=sf-dump-key>Email</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>Email Address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email Address</span>\"\n                    \"<span class=sf-dump-key>Email address</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Email address</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook chat?</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Enable Facebook chat?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in post detail page?</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Enable Facebook comment in post detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in project detail page?</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Enable Facebook comment in project detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Facebook comment in property detail page?</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Enable Facebook comment in property detail page?</span>\"\n                    \"<span class=sf-dump-key>Enable Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enable Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Enable Preloader?</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable Preloader?</span>\"\n                    \"<span class=sf-dump-key>Enable back to top button</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Enable back to top button</span>\"\n                    \"<span class=sf-dump-key>Enable dark mode</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enable dark mode</span>\"\n                    \"<span class=sf-dump-key>Enable lazy loading</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Enable lazy loading</span>\"\n                    \"<span class=sf-dump-key>Enable light mode</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable light mode</span>\"\n                    \"<span class=sf-dump-key>Enable search box</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Enable search box</span>\"\n                    \"<span class=sf-dump-key>Enable search projects on search box</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Enable search projects on search box</span>\"\n                    \"<span class=sf-dump-key>Enable sticky header</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enable sticky header</span>\"\n                    \"<span class=sf-dump-key>Enter API key into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Enter API key into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Client ID, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Enter Client ID, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Public, Secret into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Enter Public, Secret into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Enter Store ID and Store Password (API/Secret key) into the box in right hand</span>\"\n                    \"<span class=sf-dump-key>Enter Your Email</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter Your Email</span>\"\n                    \"<span class=sf-dump-key>Enter checklist here, saperated by commas (,)</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Enter checklist here, saperated by commas (,)</span>\"\n                    \"<span class=sf-dump-key>Enter keyword...</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Enter keyword...</span>\"\n                    \"<span class=sf-dump-key>Enter your message</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Enter your message</span>\"\n                    \"<span class=sf-dump-key>Enter your message...</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Enter your message...</span>\"\n                    \"<span class=sf-dump-key>Error</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Error</span>\"\n                    \"<span class=sf-dump-key>Error when processing payment via :paymentType!</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Error when processing payment via :paymentType!</span>\"\n                    \"<span class=sf-dump-key>Ex: 60-Day Job Postings</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Ex: 60-Day Job Postings</span>\"\n                    \"<span class=sf-dump-key>Expand the content of the first FAQ</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Expand the content of the first FAQ</span>\"\n                    \"<span class=sf-dump-key>Explore Now</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Explore Now</span>\"\n                    \"<span class=sf-dump-key>Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property&#039;s convenience.</span>\" => \"<span class=sf-dump-str>-</span>\"\n                    \"<span class=sf-dump-key>FAQ categories</span>\" => \"<span class=sf-dump-str title=\"14 characters\">FAQ categories</span>\"\n                    \"<span class=sf-dump-key>FAQs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FAQs</span>\"\n                    \"<span class=sf-dump-key>Facebook</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Facebook</span>\"\n                    \"<span class=sf-dump-key>Facebook Admin ID</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Facebook Admin ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Admins</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook Admins</span>\"\n                    \"<span class=sf-dump-key>Facebook App ID</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Facebook App ID</span>\"\n                    \"<span class=sf-dump-key>Facebook Integration</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Facebook Integration</span>\"\n                    \"<span class=sf-dump-key>Facebook admins to manage comments :link</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Facebook admins to manage comments :link</span>\"\n                    \"<span class=sf-dump-key>Facebook page ID</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Facebook page ID</span>\"\n                    \"<span class=sf-dump-key>Featured</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Featured</span>\"\n                    \"<span class=sf-dump-key>Featured projects</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Featured projects</span>\"\n                    \"<span class=sf-dump-key>Featured properties</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Featured properties</span>\"\n                    \"<span class=sf-dump-key>Features</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Features</span>\"\n                    \"<span class=sf-dump-key>Filter</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Filter</span>\"\n                    \"<span class=sf-dump-key>Filter box on the left</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Filter box on the left</span>\"\n                    \"<span class=sf-dump-key>Find Projects</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Find Projects</span>\"\n                    \"<span class=sf-dump-key>Find Properties</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Find Properties</span>\"\n                    \"<span class=sf-dump-key>First name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">First name</span>\"\n                    \"<span class=sf-dump-key>Fix it for me</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Fix it for me</span>\"\n                    \"<span class=sf-dump-key>Flat Range</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Flat Range</span>\"\n                    \"<span class=sf-dump-key>Flat from</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat from</span>\"\n                    \"<span class=sf-dump-key>Flat to</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Flat to</span>\"\n                    \"<span class=sf-dump-key>Flats:</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Flats:</span>\"\n                    \"<span class=sf-dump-key>Floor Plans</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Floor Plans</span>\"\n                    \"<span class=sf-dump-key>Floor plans</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Floor plans</span>\"\n                    \"<span class=sf-dump-key>Floors</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Floors</span>\"\n                    \"<span class=sf-dump-key>Floors:</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Floors:</span>\"\n                    \"<span class=sf-dump-key>Follow Us</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Follow Us</span>\"\n                    \"<span class=sf-dump-key>Follow Us:</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Follow Us:</span>\"\n                    \"<span class=sf-dump-key>Footer (after)</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Footer (after)</span>\"\n                    \"<span class=sf-dump-key>Footer (before)</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Footer (before)</span>\"\n                    \"<span class=sf-dump-key>Footer background color</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Footer background color</span>\"\n                    \"<span class=sf-dump-key>Footer background image</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Footer background image</span>\"\n                    \"<span class=sf-dump-key>For Rent</span>\" => \"<span class=sf-dump-str title=\"8 characters\">For Rent</span>\"\n                    \"<span class=sf-dump-key>For Sale</span>\" => \"<span class=sf-dump-str title=\"8 characters\">For Sale</span>\"\n                    \"<span class=sf-dump-key>For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\" => \"<span class=sf-dump-str title=\"91 characters\">For devices with width from 768px to 1200px, if empty, will use the image from the desktop.</span>\"\n                    \"<span class=sf-dump-key>For devices with width less than 768px, if empty, will use the image from the tablet.</span>\" => \"<span class=sf-dump-str title=\"85 characters\">For devices with width less than 768px, if empty, will use the image from the tablet.</span>\"\n                    \"<span class=sf-dump-key>Forgot Password</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Forgot Password</span>\"\n                    \"<span class=sf-dump-key>Forgot password?</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Forgot password?</span>\"\n                    \"<span class=sf-dump-key>Free</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Free</span>\"\n                    \"<span class=sf-dump-key>Free :number post(s)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Free :number post(s)</span>\"\n                    \"<span class=sf-dump-key>From</span>\" => \"<span class=sf-dump-str title=\"4 characters\">From</span>\"\n                    \"<span class=sf-dump-key>Full Width</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Full Width</span>\"\n                    \"<span class=sf-dump-key>Functions</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Functions</span>\"\n                    \"<span class=sf-dump-key>Gallery</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Gallery</span>\"\n                    \"<span class=sf-dump-key>Go to :link to change the copyright text.</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Go to :link to change the copyright text.</span>\"\n                    \"<span class=sf-dump-key>Go to homepage</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Go to homepage</span>\"\n                    \"<span class=sf-dump-key>Google Maps</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Google Maps</span>\"\n                    \"<span class=sf-dump-key>Grid</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Grid</span>\"\n                    \"<span class=sf-dump-key>Group by category</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Group by category</span>\"\n                    \"<span class=sf-dump-key>HTML code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">HTML code</span>\"\n                    \"<span class=sf-dump-key>Header (after)</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Header (after)</span>\"\n                    \"<span class=sf-dump-key>Header (before)</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Header (before)</span>\"\n                    \"<span class=sf-dump-key>Heading</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Heading</span>\"\n                    \"<span class=sf-dump-key>Heading 1</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 1</span>\"\n                    \"<span class=sf-dump-key>Heading 2</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 2</span>\"\n                    \"<span class=sf-dump-key>Heading 3</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 3</span>\"\n                    \"<span class=sf-dump-key>Heading 4</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 4</span>\"\n                    \"<span class=sf-dump-key>Heading 5</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 5</span>\"\n                    \"<span class=sf-dump-key>Heading 6</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Heading 6</span>\"\n                    \"<span class=sf-dump-key>Height</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Height</span>\"\n                    \"<span class=sf-dump-key>Hello</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hello</span>\"\n                    \"<span class=sf-dump-key>Hero Banner</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hero Banner</span>\"\n                    \"<span class=sf-dump-key>Hide Advanced</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Hide Advanced</span>\"\n                    \"<span class=sf-dump-key>Home</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Home</span>\"\n                    \"<span class=sf-dump-key>Homepage</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Homepage</span>\"\n                    \"<span class=sf-dump-key>Hotline</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Hotline</span>\"\n                    \"<span class=sf-dump-key>Hours</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hours</span>\"\n                    \"<span class=sf-dump-key>Hover color</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Hover color</span>\"\n                    \"<span class=sf-dump-key>I agree to the :link</span>\" => \"<span class=sf-dump-str title=\"20 characters\">I agree to the :link</span>\"\n                    \"<span class=sf-dump-key>I agree to the Terms and Privacy Policy</span>\" => \"<span class=sf-dump-str title=\"39 characters\">I agree to the Terms and Privacy Policy</span>\"\n                    \"<span class=sf-dump-key>Icon</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Icon</span>\"\n                    \"<span class=sf-dump-key>Icon Image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon Image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>Icon image</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Icon image</span>\"\n                    \"<span class=sf-dump-key>Icon image (It will override icon above if set)</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Icon image (It will override icon above if set)</span>\"\n                    \"<span class=sf-dump-key>If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.</span>\" => \"<span class=sf-dump-str title=\"109 characters\">If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.</span>\"\n                    \"<span class=sf-dump-key>If icon image is set, it will be used instead of the icon above.</span>\" => \"<span class=sf-dump-str title=\"64 characters\">If icon image is set, it will be used instead of the icon above.</span>\"\n                    \"<span class=sf-dump-key>If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\" => \"<span class=sf-dump-str title=\"187 characters\">If you are the administrator and you can&#039;t access your site after enabling maintenance mode, just need to delete file &lt;strong&gt;storage/framework/down&lt;/strong&gt; to turn-off maintenance mode.</span>\"\n                    \"<span class=sf-dump-key>If you need help, contact us at :mail.</span>\" => \"<span class=sf-dump-str title=\"38 characters\">If you need help, contact us at :mail.</span>\"\n                    \"<span class=sf-dump-key>If you select an image, the background color will be ignored.</span>\" => \"<span class=sf-dump-str title=\"61 characters\">If you select an image, the background color will be ignored.</span>\"\n                    \"<span class=sf-dump-key>If you use the YouTube video link above, the thumbnail will be automatically obtained.</span>\" => \"<span class=sf-dump-str title=\"86 characters\">If you use the YouTube video link above, the thumbnail will be automatically obtained.</span>\"\n                    \"<span class=sf-dump-key>Image</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n                    \"<span class=sf-dump-key>Image Slider</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Image Slider</span>\"\n                    \"<span class=sf-dump-key>Information box title</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Information box title</span>\"\n                    \"<span class=sf-dump-key>Inner Footer Sidebar</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Inner Footer Sidebar</span>\"\n                    \"<span class=sf-dump-key>Inner footer section for site info, menus, and newsletter.</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Inner footer section for site info, menus, and newsletter.</span>\"\n                    \"<span class=sf-dump-key>Install plugin from Marketplace</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Install plugin from Marketplace</span>\"\n                    \"<span class=sf-dump-key>Internal Server Error</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Internal Server Error</span>\"\n                    \"<span class=sf-dump-key>Invalid Data!</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid Data!</span>\"\n                    \"<span class=sf-dump-key>Invalid Transaction!</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invalid Transaction!</span>\"\n                    \"<span class=sf-dump-key>Invalid step.</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Invalid step.</span>\"\n                    \"<span class=sf-dump-key>InvalidStateException occurred while trying to login</span>\" => \"<span class=sf-dump-str title=\"52 characters\">InvalidStateException occurred while trying to login</span>\"\n                    \"<span class=sf-dump-key>Investor</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Investor</span>\"\n                    \"<span class=sf-dump-key>Investor:</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Investor:</span>\"\n                    \"<span class=sf-dump-key>Invoice detail :code</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Invoice detail :code</span>\"\n                    \"<span class=sf-dump-key>Invoice information from database, ex: invoice.code, invoice.amount, ...</span>\" => \"<span class=sf-dump-str title=\"72 characters\">Invoice information from database, ex: invoice.code, invoice.amount, ...</span>\"\n                    \"<span class=sf-dump-key>Invoices</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Invoices</span>\"\n                    \"<span class=sf-dump-key>Is autoplay?</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Is autoplay?</span>\"\n                    \"<span class=sf-dump-key>It looks as through there are no activities here.</span>\" => \"<span class=sf-dump-str title=\"49 characters\">It looks as through there are no activities here.</span>\"\n                    \"<span class=sf-dump-key>It will replace Icon Font if it is present.</span>\" => \"<span class=sf-dump-str title=\"43 characters\">It will replace Icon Font if it is present.</span>\"\n                    \"<span class=sf-dump-key>Items per row</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Items per row</span>\"\n                    \"<span class=sf-dump-key>Job summary</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Job summary</span>\"\n                    \"<span class=sf-dump-key>Jobs</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Jobs</span>\"\n                    \"<span class=sf-dump-key>Joined</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Joined</span>\"\n                    \"<span class=sf-dump-key>Joined on :date</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Joined on :date</span>\"\n                    \"<span class=sf-dump-key>Key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Key</span>\"\n                    \"<span class=sf-dump-key>Keyword</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Keyword</span>\"\n                    \"<span class=sf-dump-key>Label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Label</span>\"\n                    \"<span class=sf-dump-key>Last Updated</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Last Updated</span>\"\n                    \"<span class=sf-dump-key>Last name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Last name</span>\"\n                    \"<span class=sf-dump-key>Latest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Latest</span>\"\n                    \"<span class=sf-dump-key>Latest Properties</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Latest Properties</span>\"\n                    \"<span class=sf-dump-key>Latest posts from :site_title</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Latest posts from :site_title</span>\"\n                    \"<span class=sf-dump-key>Latitude longitude center on properties page</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Latitude longitude center on properties page</span>\"\n                    \"<span class=sf-dump-key>Lazy load images</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Lazy load images</span>\"\n                    \"<span class=sf-dump-key>Lazy load placeholder image</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Lazy load placeholder image</span>\"\n                    \"<span class=sf-dump-key>Learn More</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Learn More</span>\"\n                    \"<span class=sf-dump-key>Learn more</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Learn more</span>\"\n                    \"<span class=sf-dump-key>Learn more about Twig template: :url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Learn more about Twig template: :url</span>\"\n                    \"<span class=sf-dump-key>Leave categories empty if you want to show posts from all categories.</span>\" => \"<span class=sf-dump-str title=\"69 characters\">Leave categories empty if you want to show posts from all categories.</span>\"\n                    \"<span class=sf-dump-key>License</span>\" => \"<span class=sf-dump-str title=\"7 characters\">License</span>\"\n                    \"<span class=sf-dump-key>License Activation</span>\" => \"<span class=sf-dump-str title=\"18 characters\">License Activation</span>\"\n                    \"<span class=sf-dump-key>Limit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Limit</span>\"\n                    \"<span class=sf-dump-key>LinkedIn</span>\" => \"<span class=sf-dump-str title=\"8 characters\">LinkedIn</span>\"\n                    \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n                    \"<span class=sf-dump-key>List with map on the right</span>\" => \"<span class=sf-dump-str title=\"26 characters\">List with map on the right</span>\"\n                    \"<span class=sf-dump-key>List with map on top</span>\" => \"<span class=sf-dump-str title=\"20 characters\">List with map on top</span>\"\n                    \"<span class=sf-dump-key>Listing Page (after)</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Listing Page (after)</span>\"\n                    \"<span class=sf-dump-key>Listing Page (before)</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Listing Page (before)</span>\"\n                    \"<span class=sf-dump-key>listing</span>\" => \"<span class=sf-dump-str title=\"7 characters\">listing</span>\"\n                    \"<span class=sf-dump-key>Location</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Location</span>\"\n                    \"<span class=sf-dump-key>Similar listings</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Similar listings</span>\"\n                    \"<span class=sf-dump-key>:count photo</span>\" => \"<span class=sf-dump-str title=\"12 characters\">:count photo</span>\"\n                    \"<span class=sf-dump-key>Location type</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Location type</span>\"\n                    \"<span class=sf-dump-key>Country</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                    \"<span class=sf-dump-key>Choose Country</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Choose Country</span>\"\n                    \"<span class=sf-dump-key>Choose City</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Choose City</span>\"\n                    \"<span class=sf-dump-key>Price $/month</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Price $/month</span>\"\n                    \"<span class=sf-dump-key>Login</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Login</span>\"\n                    \"<span class=sf-dump-key>Login to your account</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Login to your account</span>\"\n                    \"<span class=sf-dump-key>Login with social networks</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Login with social networks</span>\"\n                    \"<span class=sf-dump-key>Logo height (px)</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Logo height (px)</span>\"\n                    \"<span class=sf-dump-key>Logo light</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Logo light</span>\"\n                    \"<span class=sf-dump-key>Looks like there are no reviews!</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Looks like there are no reviews!</span>\"\n                    \"<span class=sf-dump-key>Loop?</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Loop?</span>\"\n                    \"<span class=sf-dump-key>Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\" => \"<span class=sf-dump-str title=\"124 characters\">Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.</span>\"\n                    \"<span class=sf-dump-key>Main header background color</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Main header background color</span>\"\n                    \"<span class=sf-dump-key>Main header border color</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Main header border color</span>\"\n                    \"<span class=sf-dump-key>Main header text color</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Main header text color</span>\"\n                    \"<span class=sf-dump-key>Manage Invoices</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Manage Invoices</span>\"\n                    \"<span class=sf-dump-key>Manage Social Links in Appearance &#8594; Theme Options &#8594; Social links.</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Manage Social Links in Appearance &#8594; Theme Options &#8594; Social links.</span>\"\n                    \"<span class=sf-dump-key>Manage the social links in Theme Options -&gt; Social Links</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Manage the social links in Theme Options -&gt; Social Links</span>\"\n                    \"<span class=sf-dump-key>Manual Transaction</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Manual Transaction</span>\"\n                    \"<span class=sf-dump-key>Map</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Map</span>\"\n                    \"<span class=sf-dump-key>Math Captcha</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Math Captcha</span>\"\n                    \"<span class=sf-dump-key>Math Captcha Verification Failed!</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Math Captcha Verification Failed!</span>\"\n                    \"<span class=sf-dump-key>Media - Audio</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Audio</span>\"\n                    \"<span class=sf-dump-key>Media - Video</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Media - Video</span>\"\n                    \"<span class=sf-dump-key>Media URL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Media URL</span>\"\n                    \"<span class=sf-dump-key>Menu</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Menu</span>\"\n                    \"<span class=sf-dump-key>Message</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Message</span>\"\n                    \"<span class=sf-dump-key>Minutes</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Minutes</span>\"\n                    \"<span class=sf-dump-key>Mobile Image</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Mobile Image</span>\"\n                    \"<span class=sf-dump-key>More Job Openings</span>\" => \"<span class=sf-dump-str title=\"17 characters\">More Job Openings</span>\"\n                    \"<span class=sf-dump-key>More properties by this agent</span>\" => \"<span class=sf-dump-str title=\"29 characters\">More properties by this agent</span>\"\n                    \"<span class=sf-dump-key>My Profile</span>\" => \"<span class=sf-dump-str title=\"10 characters\">My Profile</span>\"\n                    \"<span class=sf-dump-key>Profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Profile</span>\"\n                    \"<span class=sf-dump-key>My Wishlist</span>\" => \"<span class=sf-dump-str title=\"11 characters\">My Wishlist</span>\"\n                    \"<span class=sf-dump-key>My favorites</span>\" => \"<span class=sf-dump-str title=\"12 characters\">My favorites</span>\"\n                    \"<span class=sf-dump-key>Commission</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Commission</span>\"\n                    \"<span class=sf-dump-key>Deposit</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Deposit</span>\"\n                    \"<span class=sf-dump-key>My listings</span>\" => \"<span class=sf-dump-str title=\"11 characters\">My listings</span>\"\n                    \"<span class=sf-dump-key>listings</span>\" => \"<span class=sf-dump-str title=\"8 characters\">listings</span>\"\n                    \"<span class=sf-dump-key>Search by parameters</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Search by parameters</span>\"\n                    \"<span class=sf-dump-key>Object type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Object type</span>\"\n                    \"<span class=sf-dump-key>Clear All</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Clear All</span>\"\n                    \"<span class=sf-dump-key>Log out</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Log out</span>\"\n                    \"<span class=sf-dump-key>Log in</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Log in</span>\"\n                    \"<span class=sf-dump-key>Sign in</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sign in</span>\"\n                    \"<span class=sf-dump-key>Sign up</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Sign up</span>\"\n                    \"<span class=sf-dump-key>Name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>Name (A-Z)</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Name (A-Z)</span>\"\n                    \"<span class=sf-dump-key>Name (Z-A)</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Name (Z-A)</span>\"\n                    \"<span class=sf-dump-key>Nearby</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Nearby</span>\"\n                    \"<span class=sf-dump-key>Newest</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Newest</span>\"\n                    \"<span class=sf-dump-key>Newsletter Popup</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Newsletter Popup</span>\"\n                    \"<span class=sf-dump-key>Newsletter form</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Newsletter form</span>\"\n                    \"<span class=sf-dump-key>Next</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Next</span>\"\n                    \"<span class=sf-dump-key>No</span>\" => \"<span class=sf-dump-str title=\"2 characters\">No</span>\"\n                    \"<span class=sf-dump-key>No Layout</span>\" => \"<span class=sf-dump-str title=\"9 characters\">No Layout</span>\"\n                    \"<span class=sf-dump-key>No cities found</span>\" => \"<span class=sf-dump-str title=\"15 characters\">No cities found</span>\"\n                    \"<span class=sf-dump-key>No payment charge. Please try again!</span>\" => \"<span class=sf-dump-str title=\"36 characters\">No payment charge. Please try again!</span>\"\n                    \"<span class=sf-dump-key>No project found</span>\" => \"<span class=sf-dump-str title=\"16 characters\">No project found</span>\"\n                    \"<span class=sf-dump-key>No projects found.</span>\" => \"<span class=sf-dump-str title=\"18 characters\">No projects found.</span>\"\n                    \"<span class=sf-dump-key>No properties found.</span>\" => \"<span class=sf-dump-str title=\"20 characters\">No properties found.</span>\"\n                    \"<span class=sf-dump-key>No results found</span>\" => \"<span class=sf-dump-str title=\"16 characters\">No results found</span>\"\n                    \"<span class=sf-dump-key>No suggestion found</span>\" => \"<span class=sf-dump-str title=\"19 characters\">No suggestion found</span>\"\n                    \"<span class=sf-dump-key>No transactions!</span>\" => \"<span class=sf-dump-str title=\"16 characters\">No transactions!</span>\"\n                    \"<span class=sf-dump-key>Number</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Number</span>\"\n                    \"<span class=sf-dump-key>Number of bathrooms</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Number of bathrooms</span>\"\n                    \"<span class=sf-dump-key>Number of bedrooms</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Number of bedrooms</span>\"\n                     &#8230;404\n                  </samp>]\n                </samp>]\n                \"<span class=sf-dump-key>Popular Cities</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => []\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>Paopular Cities</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => []\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>Hero Section</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => []\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n                \"<span class=sf-dump-key>New Properties</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => []\n                  \"<span class=sf-dump-key>en</span>\" => []\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/captcha</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>captcha</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:9</span> [ &#8230;9]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/slug</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>slug</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:4</span> [ &#8230;4]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:10</span> [ &#8230;10]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/real-estate</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>real-estate</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:9</span> [ &#8230;9]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:9</span> [ &#8230;9]\n                </samp>]\n                \"<span class=sf-dump-key>property</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:31</span> [ &#8230;31]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:38</span> [ &#8230;38]\n                </samp>]\n                \"<span class=sf-dump-key>category</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:9</span> [ &#8230;9]\n                </samp>]\n                \"<span class=sf-dump-key>project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:14</span> [ &#8230;14]\n                </samp>]\n                \"<span class=sf-dump-key>account</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:31</span> [ &#8230;31]\n                </samp>]\n                \"<span class=sf-dump-key>custom-fields</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:10</span> [ &#8230;10]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/menu</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>menu</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:25</span> [ &#8230;25]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:30</span> [ &#8230;30]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/page</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>pages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:13</span> [ &#8230;13]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>packages/widget</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>widget</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:21</span> [ &#8230;21]\n                  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:29</span> [ &#8230;29]\n                </samp>]\n              </samp>]\n              \"<span class=sf-dump-key>plugins/contact</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>ru</span>\" => <span class=sf-dump-note>array:44</span> [ &#8230;44]\n                </samp>]\n              </samp>]\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">selector</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">determineLocalesUsing</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">stringableHandlers</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">missingTranslationKeyCallback</span>: <span class=sf-dump-const>null</span>\n            #<span class=sf-dump-protected title=\"Protected property\">handleMissingTranslationKeys</span>: <span class=sf-dump-const>true</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n          #<span class=sf-dump-protected title=\"Protected property\">presenceVerifier</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\DatabasePresenceVerifier\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabasePresenceVerifier</span></span> {<a class=sf-dump-ref>#898</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">db</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2223 title=\"2 occurrences\">#223</a><samp data-depth=7 id=sf-dump-**********-ref2223 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n              #<span class=sf-dump-protected title=\"Protected property\">factory</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Connectors\\ConnectionFactory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Connectors</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ConnectionFactory</span></span> {<a class=sf-dump-ref>#224</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">connections</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>mysql</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\MySqlConnection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MySqlConnection</span></span> {<a class=sf-dump-ref>#227</a> &#8230;26}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">extensions</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">reconnector</span>: <span class=sf-dump-note>Closure($connection)</span> {<a class=sf-dump-ref>#225</a><samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span>\"\n                <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\DatabaseManager\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">DatabaseManager</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2223 title=\"2 occurrences\">#223</a>}\n                <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php\n89 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\xmetr\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Database\\DatabaseManager.php</span></span>\"\n                <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">79 to 81</span>\"\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">doctrineTypes</span>: []\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">failedRules</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">excludeAttributes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messages</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#3611</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">messages</span>: []\n            #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">data</span>: <span class=sf-dump-note>array:30</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n            \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"115 characters\">accounts/m-233/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>original_description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n            \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8175</span>\"\n            \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Testaccio, Rome, Metropolitan City of Rome Capital, Italy</span>\"\n            \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">41.875952</span>\"\n            \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12.475694</span>\"\n            \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str>2</span>\"\n            \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str>3</span>\"\n            \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str>2</span>\"\n            \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n            \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str>0</span>\"\n            \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"11 characters\">min_1_month</span>\"\n            \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">bank_statement</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">initialRules</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"23 characters\">nullable|string|max:220</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"23 characters\">nullable|string|max:350</span>\"\n            \"<span class=sf-dump-key>original_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">required|string</span>\"\n            \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"12 characters\">required|any</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"22 characters\">numeric|min:0|nullable</span>\"\n            \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">required|integer</span>\"\n            \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str title=\"33 characters\">numeric|min:0|max:100000|nullable</span>\"\n            \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str title=\"33 characters\">numeric|min:0|max:100000|nullable</span>\"\n            \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str title=\"33 characters\">numeric|min:0|max:100000|nullable</span>\"\n            \"<span class=sf-dump-key>status</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Validation\\Rules\\In\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Validation\\Rules</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">In</span></span> {<a class=sf-dump-ref>#3588</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">rule</span>: \"<span class=sf-dump-str title=\"2 characters\">in</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">values</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>NOT_AVAILABLE</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyStatusEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyStatusEnum</span></span> {<a class=sf-dump-ref>#3585</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"13 characters\">not_available</span>\"\n                </samp>}\n                \"<span class=sf-dump-key>RENTING</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyStatusEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyStatusEnum</span></span> {<a class=sf-dump-ref>#3586</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"\n                </samp>}\n                \"<span class=sf-dump-key>RENTED</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyStatusEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyStatusEnum</span></span> {<a class=sf-dump-ref>#3587</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"6 characters\">rented</span>\"\n                </samp>}\n              </samp>]\n            </samp>}\n            \"<span class=sf-dump-key>latitude</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">max:20</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"48 characters\">regex:/^[-]?(([0-8]?[0-9])\\.(\\d+))|(90(\\.0+)?)$/</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>longitude</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">max:20</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"65 characters\">regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\\.(\\d+))|180(\\.0+)?)$/</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">max:10000</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">rules</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>name</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">max:220</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">max:350</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>original_description</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">any</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">min:0</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">min:0</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">max:100000</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">min:0</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">max:100000</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">numeric</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">min:0</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">max:100000</span>\"\n              <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">in:&quot;not_available&quot;,&quot;renting&quot;,&quot;rented&quot;</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>latitude</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">max:20</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"48 characters\">regex:/^[-]?(([0-8]?[0-9])\\.(\\d+))|(90(\\.0+)?)$/</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>longitude</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">max:20</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"65 characters\">regex:/^[-]?((((1[0-7][0-9])|([0-9]?[0-9]))\\.(\\d+))|180(\\.0+)?)$/</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">nullable</span>\"\n              <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n              <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">max:10000</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">currentRule</span>: \"<span class=sf-dump-str title=\"3 characters\">any</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">implicitAttributes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">implicitAttributesFormatter</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">distinctValues</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">after</span>: []\n          +<span class=sf-dump-public title=\"Public property\">customMessages</span>: []\n          +<span class=sf-dump-public title=\"Public property\">fallbackMessages</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>captcha</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Captcha Verification Failed!</span>\"\n            \"<span class=sf-dump-key>math_captcha</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Math Captcha Verification Failed!</span>\"\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">customAttributes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>facilities.*.distance</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Distance key between facilities</span>\"\n            \"<span class=sf-dump-key>custom_fields.*.name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Custom Fields</span>\"\n            \"<span class=sf-dump-key>custom_fields.*.value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Custom Fields</span>\"\n            \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Floor plans</span>\"\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">customValues</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">stopOnFirstFailure</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">excludeUnvalidatedArrayKeys</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">extensions</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>no_js_validation</span>\" => <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#922</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Xmetr\\JsValidation\\Providers\\JsValidationServiceProvider\n56 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Xmetr\\JsValidation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">JsValidationServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\JsValidation\\Providers\\JsValidationServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\JsValidation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">JsValidationServiceProvider</span></span> {<a class=sf-dump-ref>#255</a> &#8230;}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\Providers\\JsValidationServiceProvider.php\n94 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\xmetr\\platform\\core</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">js-validation\\src\\Providers\\JsValidationServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">33 to 35</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>captcha</span>\" => <span class=sf-dump-note>Closure($attribute, $value, $parameters)</span> {<a class=sf-dump-ref>#2206</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Xmetr\\Captcha\\Providers\\CaptchaServiceProvider\n46 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Xmetr\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Captcha\\Providers\\CaptchaServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2870 title=\"2 occurrences\">#870</a> &#8230;}\n              <span class=sf-dump-meta>use</span>: {<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-meta>$app</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n              </samp>}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\xmetr\\platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\xmetr</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">189 to 207</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>math_captcha</span>\" => <span class=sf-dump-note>Closure($attribute, $value)</span> {<a class=sf-dump-ref>#2207</a><samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Xmetr\\Captcha\\Providers\\CaptchaServiceProvider\n46 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Xmetr\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span>\"\n              <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Captcha\\Providers\\CaptchaServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Captcha\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CaptchaServiceProvider</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2870 title=\"2 occurrences\">#870</a> &#8230;}\n              <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\xmetr\\platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\xmetr</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">platform\\plugins\\captcha\\src\\Providers\\CaptchaServiceProvider.php</span></span>\"\n              <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">209 to 215</span>\"\n            </samp>}\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">replacers</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fileRules</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">Dimensions</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">Extensions</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">File</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">Mimes</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">Mimetypes</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">implicitRules</span>: <span class=sf-dump-note>array:23</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Accepted</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">Declined</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">Filled</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">Missing</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">Present</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"8 characters\">Required</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dependentRules</span>: <span class=sf-dump-note>array:37</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">After</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"12 characters\">AfterOrEqual</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">Before</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"13 characters\">BeforeOrEqual</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">Confirmed</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">Different</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">AcceptedIf</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">DeclinedIf</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"10 characters\">RequiredIf</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredIfAccepted</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"14 characters\">RequiredUnless</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">RequiredWith</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithAll</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"15 characters\">RequiredWithout</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"18 characters\">RequiredWithoutAll</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"9 characters\">PresentIf</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"13 characters\">PresentUnless</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">PresentWith</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"14 characters\">PresentWithAll</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"10 characters\">Prohibited</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"12 characters\">ProhibitedIf</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"16 characters\">ProhibitedUnless</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"9 characters\">Prohibits</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"9 characters\">MissingIf</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"13 characters\">MissingUnless</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"11 characters\">MissingWith</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"14 characters\">MissingWithAll</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"4 characters\">Same</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"6 characters\">Unique</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">excludeRules</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Exclude</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">ExcludeIf</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">ExcludeUnless</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">ExcludeWith</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"14 characters\">ExcludeWithout</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">sizeRules</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">Size</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Between</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">Min</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">Max</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"2 characters\">Gt</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"2 characters\">Lt</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">Gte</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"3 characters\">Lte</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">numericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">defaultNumericRules</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">Numeric</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"7 characters\">Integer</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">Decimal</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">exception</span>: \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Validation\\ValidationException</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">ensureExponentWithinAllowedRangeUsing</span>: <span class=sf-dump-const>null</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>459</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">validateAttribute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">Any</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Validation/Validator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>494</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">passes</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">fails</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Validation\\Validator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"91 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">validateResolved</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\Foundation\\Http\\FormRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1302</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Providers\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Illuminate\\Foundation\\Providers\\FormRequestServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1266</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">fireCallbackArray</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($resolved)</span> {<a class=sf-dump-ref>#118</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider\n58 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormRequestServiceProvider</span></span>\"\n          <span class=sf-dump-meta>this</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Providers\\FormRequestServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Providers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormRequestServiceProvider</span></span> {<a class=sf-dump-ref>#85</a> &#8230;}\n          <span class=sf-dump-meta>file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php\n112 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">D:\\laragon\\www\\xmetr\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php</span></span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">29 to 31</span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1252</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"27 characters\">fireAfterResolvingCallbacks</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>813</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"22 characters\">fireResolvingCallbacks</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>986</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>731</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>971</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>85</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">transformDependency</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">[object ReflectionParameter]</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">[object stdClass]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"25 characters\">resolveMethodDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => []\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"25 characters\">[object ReflectionMethod]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>59</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">resolveClassMethodDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => []\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"75 characters\">[object Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"17 characters\">resolveParameters</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"75 characters\">[object Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>259</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"75 characters\">[object Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>205</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>806</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">platform/plugins/real-estate/src/Http/Middleware/EnsureAccountIsApproved.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"56 characters\">Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">platform/plugins/language/src/Http/Middleware/LocalizationRedirectFilter.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>53</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">platform/plugins/language/src/Http/Middleware/LocaleSessionRedirect.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Xmetr\\Base\\Http\\Middleware\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">platform/core/base/src/Http/Middleware/CoreMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\Base\\Http\\Middleware\\CoreMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">platform/core/base/src/Http/Middleware/HttpsProtocolMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>16</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">platform/core/base/src/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"84 characters\">platform/packages/installer/src/Http/Middleware/RedirectIfNotInstalledMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"64 characters\">Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"55 characters\">app/Http/Middleware/RedirectIncorrectLanguagePrefix.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"73 characters\">platform/plugins/real-estate/src/Http/Middleware/RedirectIfNotAccount.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>24</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Xmetr\\RealEstate\\Http\\Middleware\\RedirectIfNotAccount</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Xmetr\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>85</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>86</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>87</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->callExtension($rule, $parameters);\n", "        }\n", "\n", "        throw new BadMethodCallException(sprintf(\n", "            'Method %s::%s does not exist.', static::class, $method\n", "        ));\n", "    }\n"], "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FValidator.php:1646", "ajax": false, "filename": "Validator.php", "line": "1646"}}]}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00091, "accumulated_duration_str": "910μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `re_accounts` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "account", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\RedirectIfNotAccount.php", "line": 16}], "start": **********.032869, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 48.352}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 230}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}], "start": **********.061806, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 48.352, "width_percent": 51.648}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create.store", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store", "uri": "POST en/account/properties/create", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:91\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts", "prefix": "en/account/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:91\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php:91-175</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, account, Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved", "duration": "1.43s", "peak_memory": "50MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"115 characters\">accounts/m-233/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>original_description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8175</span>\"\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Testaccio, Rome, Metropolitan City of Rome Capital, Italy</span>\"\n  \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">41.875952</span>\"\n  \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12.475694</span>\"\n  \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"11 characters\">min_1_month</span>\"\n  \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">bank_statement</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-838347128 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3852</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynxFLDvjxRSusjMTA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6IjBkNDVlYTc2LTZmNjEtNGU3ZS04ZDJiLTdjYmM4OGE0Zjc5ZSIsImMiOjE3NTM0Nzg1MzY4MDMsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753478537$o105$g1$t1753478577$j20$l0$h0; XSRF-TOKEN=eyJpdiI6Ims4Z252NytSQWxrMU1ZcXVuWUVnNmc9PSIsInZhbHVlIjoiWlp2YVk4ZHErR3NNOTdERSt5VFIzYkZyZEhGWlpWSkpuVnhIRnBCWnZPSjR2ZFJKZXpyN0RzRk83cm80clFKMXQ0a29vVXJNbEJSNzcxZi9kNWdPWm1ndmZaOExuWUZXa1RnTXlFR0VCbkVUWEFCOVkzZUYzTExCR2xBOUxOUUYiLCJtYWMiOiI5ZmFlYTNhNjg5MjA1ZmZkZjcxNGMzN2MwNDEzNTk0NjJlMzA5MTc2ZjI0NTJiMTBlYTZkZTdhNzdlOGViZTE2IiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IlJjZXNaWGQ5cWR4b3dkQzZyd1JTOXc9PSIsInZhbHVlIjoiajdyTHFGWmJab3Z3YWpYVFFudUhoS1lPS1JQVkU5cFJuUHlqTWtJQmRpNWJyclRRNERiQU1yUzZJYnpOME4razNBSStBWUtQdFlVOFA3MTFjTTBqcXowclZ0Yi9POW51V0FxcGxvQmdoSWVQM3BHb1R4TVdCZXQrbCtGVnZnSmIiLCJtYWMiOiIzYmQ2ZDI3M2I1YWE1OTM4ODQ1Yzc3NTlkNjBjMmVkNTJjM2QwYTAzY2IzOGQ4YjhhNTJmZDBhMWZjNTY4OGVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838347128\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-436862956 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sr8ZvB9DucEYA9CROr9RC5CONMte5XinBBSwaJ1i</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436862956\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1362230815 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 21:38:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1362230815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create.store", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store"}, "badge": "500 Internal Server Error"}}