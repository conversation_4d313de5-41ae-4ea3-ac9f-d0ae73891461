[2025-07-25 16:43:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:40] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:40] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 16:43:40] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 17:21:31] local.ERROR: Command "r:l" is ambiguous.
Did you mean one of these?
    route:list                  List all registered routes
    revert:language-aware-query Revert the language-aware slug query to original state. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"r:l\" is ambiguous.
Did you mean one of these?
    route:list                  List all registered routes
    revert:language-aware-query Revert the language-aware slug query to original state. at D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php:778)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('r:l')
#1 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\laragon\\www\\xmetr\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-25 17:33:19] local.WARNING: DB query exceeded 2485.95 ms. SQL: select * from `slugs` where (`key` = ? and `reference_type` = ? and `prefix` = ?) or exists (select * from `slugs_translations` where `slugs`.`id` = `slugs_translations`.`slugs_id` and (`key` = ? and `reference_type` = ? and `prefix` = ?)) limit 1  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:54] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:54] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:31:54] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:11] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:32] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:36:50] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:37:20] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:42:38] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:43:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:43:40] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:44:58] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:16] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:45:25] local.ERROR: Partial view [real-estate.projects.items] not found. {"userId":1,"exception":"[object] (Xmetr\\Theme\\Exceptions\\UnknownPartialFileException(code: 0): Partial view [real-estate.projects.items] not found. at D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php:496)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(485): Xmetr\\Theme\\Theme->loadPartial('real-estate.pro...', 'theme.xmetr::pa...', Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->partial('real-estate.pro...', Array)
#2 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicController.php(528): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PublicController->getProjectsByCountry('poland', Object(Illuminate\\Http\\Request))
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getProjectsByCo...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PublicController), 'getProjectsByCo...')
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 {main}
"} 
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:46:50] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:48:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:54:41] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:58:41] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:58:54] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:05] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:05] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:13] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:14] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:16] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:21] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:21] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:39] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 18:59:39] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:23] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:32:34] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:34] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:32:50] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:33:51] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:34:02] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:34:02] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:38] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:35:47] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:35:47] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:36:42] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:36:47] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:36:55] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:36:56] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:37:06] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:37:06] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:38:52] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:39:00] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:00] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:39:40] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:39:42] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:39:51] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:39:55] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:40:17] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:17] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:21] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:40:22] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:40:30] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:40:44] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:41:37] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:41:44] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:41:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:24] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:42:30] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:30] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:42:59] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:43:06] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:43:07] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:46:13] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:46:13] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:47:18] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:47:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:47:44] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:47:44] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:21] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:48:21] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:48:55] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:24] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:49:24] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:33] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:49:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:48] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:49:53] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:49:53] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:56:09] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:56:09] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 19:56:46] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 19:56:46] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:00:34] local.ERROR: Undefined property: Illuminate\Database\Eloquent\Relations\MorphMany::$reviews_avg_star {"view":{"view":"D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\partials\\project.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-660107729 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3625</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-660107729\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","property":"<pre class=sf-dump id=sf-dump-1025777282 data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Property</span> {<a class=sf-dump-ref href=#sf-dump-1025777282-ref23700 title=\"5 occurrences\">#3700</a><samp data-depth=1 id=sf-dump-1025777282-ref23700 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_properties</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:53</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1172</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Long-term rent of a 15m&#178; studio in Paris, France</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>original_description</span>\" => \"\"\"
      <span class=sf-dump-str title=\"198 characters\">&#1057;&#1090;&#1091;&#1076;&#1080;&#1103;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128208; 15m2<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128205; rue d la Tombe Issoire 75014, Paris <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128727; 3 &#1101;&#1090;&#1072;&#1078;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128293; &#1054;&#1090;&#1086;&#1087;&#1083;&#1077;&#1085;&#1080;&#1077; &#1080;&#1085;&#1076;&#1080;&#1074;&#1080;&#1076;&#1091;&#1072;&#1083;&#1100;&#1085;&#1086;&#1077; &#1101;&#1083;&#1077;&#1082;&#1090;&#1088;&#1080;&#1095;&#1077;&#1089;&#1082;&#1086;&#1077;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1051;&#1080;&#1092;&#1090; &#10060;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1048;&#1085;&#1090;&#1077;&#1088;&#1085;&#1077;&#1090; &#10060;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1057;&#1090;&#1080;&#1088;&#1072;&#1083;&#1100;&#1085;&#1072;&#1103; &#1084;&#1072;&#1096;&#1080;&#1085;&#1082;&#1072; &#9989;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1058;&#1091;&#1072;&#1083;&#1077;&#1090; &#128701; &#9989;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128176; 800&#8364;/ &#1084;&#1077;&#1089;&#1103;&#1094;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128467;&#65039; &#1082;&#1074;&#1072;&#1088;&#1090;&#1080;&#1088;&#1072; c &#1072;&#1074;&#1075;&#1091;&#1089;&#1090;&#1072;</span>
      \"\"\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"213 characters\">&lt;p&gt;Studio&lt;br&gt;&#128208; 15m2&lt;br&gt;&#128205; rue d la Tombe Issoire 75014, Paris&lt;br&gt;&#128727; 3 floor&lt;br&gt;&#128293; Individual electric heating&lt;br&gt;Lift &#10060;&lt;br&gt;Internet &#10060;&lt;br&gt;Washing machine &#9989;&lt;br&gt;Toilet &#128701; &#9989;&lt;br&gt;&#128176; 800&#8364;/month&lt;br&gt;&#128467;&#65039; apartment from August&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"45 characters\">75014 Rue de la Tombe Issoire, &#1055;&#1072;&#1088;&#1080;&#1078;, &#1060;&#1088;&#1072;&#1085;&#1094;&#1110;&#1103;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"267 characters\">{&quot;1&quot;:&quot;.\\/photo-2025-06-19-210338.webp&quot;,&quot;2&quot;:&quot;.\\/photo-2025-06-19-210337.webp&quot;,&quot;3&quot;:&quot;.\\/photo-2025-06-19-210336.webp&quot;,&quot;4&quot;:&quot;.\\/photo-2025-06-19-210335.webp&quot;,&quot;5&quot;:&quot;.\\/photo-2025-06-19-210334.webp&quot;,&quot;6&quot;:&quot;.\\/photo-2025-06-19-210333.webp&quot;,&quot;7&quot;:&quot;.\\/photo-2025-06-19-210332.webp&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => \"<span class=sf-dump-str title=\"29 characters\">accounts/m-229/testvideo1.mp4</span>\"
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>videos</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>floor_plans</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>15.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">800.00</span>\"
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8189</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-1025777282-ref24415 title=\"2 occurrences\">#4415</a><samp data-depth=3 id=sf-dump-1025777282-ref24415 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    </samp>}
    \"<span class=sf-dump-key>rental_period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\RentalPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RentalPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-1025777282-ref24456 title=\"2 occurrences\">#4456</a><samp data-depth=3 id=sf-dump-1025777282-ref24456 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"12 characters\">min_6_months</span>\"
    </samp>}
    \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>suitable_for</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;students&quot;,&quot;professionals&quot;,&quot;families&quot;]</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-19</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>bills_included</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>furnished</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>pets_allowed</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>smoking_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>online_view_tour</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-19 21:05:45</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 15:40:11</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"10 characters\">48.8278228</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">2.3325422</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>27</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>price_formatted</span>\" => \"<span class=sf-dump-str title=\"6 characters\">$ 909 </span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:52</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1172</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Long-term rent of a 15m&#178; studio in Paris, France</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">rent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>original_description</span>\" => \"\"\"
      <span class=sf-dump-str title=\"198 characters\">&#1057;&#1090;&#1091;&#1076;&#1080;&#1103;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128208; 15m2<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128205; rue d la Tombe Issoire 75014, Paris <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128727; 3 &#1101;&#1090;&#1072;&#1078;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128293; &#1054;&#1090;&#1086;&#1087;&#1083;&#1077;&#1085;&#1080;&#1077; &#1080;&#1085;&#1076;&#1080;&#1074;&#1080;&#1076;&#1091;&#1072;&#1083;&#1100;&#1085;&#1086;&#1077; &#1101;&#1083;&#1077;&#1082;&#1090;&#1088;&#1080;&#1095;&#1077;&#1089;&#1082;&#1086;&#1077;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1051;&#1080;&#1092;&#1090; &#10060;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1048;&#1085;&#1090;&#1077;&#1088;&#1085;&#1077;&#1090; &#10060;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1057;&#1090;&#1080;&#1088;&#1072;&#1083;&#1100;&#1085;&#1072;&#1103; &#1084;&#1072;&#1096;&#1080;&#1085;&#1082;&#1072; &#9989;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#1058;&#1091;&#1072;&#1083;&#1077;&#1090; &#128701; &#9989;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128176; 800&#8364;/ &#1084;&#1077;&#1089;&#1103;&#1094;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>
      <span class=sf-dump-str title=\"198 characters\">&#128467;&#65039; &#1082;&#1074;&#1072;&#1088;&#1090;&#1080;&#1088;&#1072; c &#1072;&#1074;&#1075;&#1091;&#1089;&#1090;&#1072;</span>
      \"\"\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"213 characters\">&lt;p&gt;Studio&lt;br&gt;&#128208; 15m2&lt;br&gt;&#128205; rue d la Tombe Issoire 75014, Paris&lt;br&gt;&#128727; 3 floor&lt;br&gt;&#128293; Individual electric heating&lt;br&gt;Lift &#10060;&lt;br&gt;Internet &#10060;&lt;br&gt;Washing machine &#9989;&lt;br&gt;Toilet &#128701; &#9989;&lt;br&gt;&#128176; 800&#8364;/month&lt;br&gt;&#128467;&#65039; apartment from August&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"45 characters\">75014 Rue de la Tombe Issoire, &#1055;&#1072;&#1088;&#1080;&#1078;, &#1060;&#1088;&#1072;&#1085;&#1094;&#1110;&#1103;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"267 characters\">{&quot;1&quot;:&quot;.\\/photo-2025-06-19-210338.webp&quot;,&quot;2&quot;:&quot;.\\/photo-2025-06-19-210337.webp&quot;,&quot;3&quot;:&quot;.\\/photo-2025-06-19-210336.webp&quot;,&quot;4&quot;:&quot;.\\/photo-2025-06-19-210335.webp&quot;,&quot;5&quot;:&quot;.\\/photo-2025-06-19-210334.webp&quot;,&quot;6&quot;:&quot;.\\/photo-2025-06-19-210333.webp&quot;,&quot;7&quot;:&quot;.\\/photo-2025-06-19-210332.webp&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => \"<span class=sf-dump-str title=\"29 characters\">accounts/m-229/testvideo1.mp4</span>\"
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>videos</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>floor_plans</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>project_id</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>square</span>\" => <span class=sf-dump-num>15.0</span>
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">800.00</span>\"
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8189</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"5 characters\">month</span>\"
    \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"12 characters\">min_6_months</span>\"
    \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>suitable_for</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;students&quot;,&quot;professionals&quot;,&quot;families&quot;]</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">renting</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">approved</span>\"
    \"<span class=sf-dump-key>reject_reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-19</span>\"
    \"<span class=sf-dump-key>auto_renew</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>bills_included</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>furnished</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>pets_allowed</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>smoking_allowed</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>online_view_tour</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>never_expired</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-19 21:05:45</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-09 15:40:11</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"10 characters\">48.8278228</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">2.3325422</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>27</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:29</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyStatusEnum</span>\"
    \"<span class=sf-dump-key>moderation_status</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Xmetr\\RealEstate\\Enums\\ModerationStatusEnum</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Xmetr\\RealEstate\\Enums\\PropertyTypeEnum</span>\"
    \"<span class=sf-dump-key>period</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>private_notes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"
    \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>utilities</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>floor_plans</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>telegram_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>phone_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Xmetr\\RealEstate\\Enums\\RentalPeriodEnum</span>\"
    \"<span class=sf-dump-key>required_documents</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
    \"<span class=sf-dump-key>suitable_for</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\PropertyPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PropertyPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-1025777282-ref24415 title=\"2 occurrences\">#4415</a>}
    \"<span class=sf-dump-key>rental_period</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Enums\\RentalPeriodEnum
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">RentalPeriodEnum</span></span> {<a class=sf-dump-ref href=#sf-dump-1025777282-ref24456 title=\"2 occurrences\">#4456</a>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#3705</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>city</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\City
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">City</span></span> {<a class=sf-dump-ref>#4240</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">cities</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [ &#8230;12]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4263</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>currency</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Currency
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Currency</span></span> {<a class=sf-dump-ref>#4291</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">re_currencies</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>country</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\Country
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Country</span></span> {<a class=sf-dump-ref>#4349</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">countries</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [ &#8230;10]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4346</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>author</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Account
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Account</span></span> {<a class=sf-dump-ref>#4315</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_accounts</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:26</span> [ &#8230;26]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:26</span> [ &#8230;26]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:15</span> [ &#8230;15]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [ &#8230;18]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
    </samp>}
    \"<span class=sf-dump-key>features</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4514</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>facilities</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4502</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>project</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#4471</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_projects</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:38</span> [ &#8230;38]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:38</span> [ &#8230;38]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:22</span> [ &#8230;22]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:35</span> [ &#8230;35]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:42</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">original_description</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">video</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"15 characters\">video_thumbnail</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"10 characters\">project_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">number_bedroom</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"15 characters\">number_bathroom</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">number_floor</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"6 characters\">square</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">currency_id</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">district_id</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"6 characters\">period</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"9 characters\">author_id</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"11 characters\">author_type</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">expire_date</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"10 characters\">auto_renew</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"14 characters\">bills_included</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"9 characters\">utilities</span>\"
    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"9 characters\">furnished</span>\"
    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"12 characters\">pets_allowed</span>\"
    <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"15 characters\">smoking_allowed</span>\"
    <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"16 characters\">online_view_tour</span>\"
    <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"8 characters\">latitude</span>\"
    <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"9 characters\">longitude</span>\"
    <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"9 characters\">unique_id</span>\"
    <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"
    <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"11 characters\">floor_plans</span>\"
    <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"13 characters\">reject_reason</span>\"
    <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"13 characters\">rental_period</span>\"
    <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"18 characters\">required_documents</span>\"
    <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"12 characters\">suitable_for</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1025777282\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","images":"<pre class=sf-dump id=sf-dump-1483300594 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210338.webp</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210337.webp</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210336.webp</span>\"
  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210335.webp</span>\"
  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210334.webp</span>\"
  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210333.webp</span>\"
  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210332.webp</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1483300594\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","style":"<pre class=sf-dump id=sf-dump-1605847129 data-indent-pad=\"  \">\"<span class=sf-dump-str>5</span>\"
</pre><script>Sfdump(\"sf-dump-1605847129\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","data":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Long-term rent of a 15m&#178; studio in Paris, France</span>\"
  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">$ 909 </span>\"
  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Paris</span>\"
  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"6 characters\">France</span>\"
  \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Apartment</span>\"
  \"<span class=sf-dump-key>number_bedroom</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>number_bathroom</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-num>3</span>
  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"5 characters\">15 &#1084;&#178;</span>\"
  \"<span class=sf-dump-key>deposit</span>\" => \"\"
  \"<span class=sf-dump-key>commission</span>\" => \"\"
  \"<span class=sf-dump-key>description</span>\" => \"\"
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210338.webp</span>\"
  \"<span class=sf-dump-key>property_url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">https://xmetr.gc/en/properties/long-term-rent-of-a-15m2-studio-in-paris-france-3</span>\"
  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210338.webp</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210337.webp</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210336.webp</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210335.webp</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210334.webp</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210333.webp</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"54 characters\">https://media.xmetr.com/./photo-2025-06-19-210332.webp</span>\"
  </samp>]
  \"<span class=sf-dump-key>agent</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Alexander </span>\"
  \"<span class=sf-dump-key>agent_phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+33616581743</span>\"
  \"<span class=sf-dump-key>agent_email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"
  \"<span class=sf-dump-key>agent_whatsapp</span>\" => \"\"
  \"<span class=sf-dump-key>agent_telegram</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+33616581743</span>\"
  \"<span class=sf-dump-key>agent_url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://xmetr.gc/en/agents/alexanderparis</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","deposit":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","image":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"30 characters\">./photo-2025-06-19-210332.webp</span>\"
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","account":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Account</span> {<a class=sf-dump-ref>#4315</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_accounts</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:26</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Alexander</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"10 characters\">rent-paris</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$.PrumdRBUSEJnbA.vJUE3OLVw0SQo48F8KuKS.QRqLJ2p23TcytzO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-05</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+33616581743</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-02 17:21:12</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-02 20:21:12</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-06 02:09:01</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">agent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>27</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>192</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8189</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:26</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Alexander</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>gender</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"10 characters\">rent-paris</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$.PrumdRBUSEJnbA.vJUE3OLVw0SQo48F8KuKS.QRqLJ2p23TcytzO</span>\"
    \"<span class=sf-dump-key>avatar_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-05-05</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+33616581743</span>\"
    \"<span class=sf-dump-key>credits</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>confirmed_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-02 17:21:12</span>\"
    \"<span class=sf-dump-key>email_verify_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-02 20:21:12</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-06 02:09:01</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_verified</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>is_public_profile</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">agent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"\"
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>27</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-num>192</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8189</span>
    \"<span class=sf-dump-key>approved_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>dob</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_start_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>package_end_date</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_public_profile</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_verified</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>first_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>last_name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>username</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>company</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"
    \"<span class=sf-dump-key>approved_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>account_type</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Xmetr\\RealEstate\\Enums\\AccountTypeEnum</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>metadata</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#4299</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#4416</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">first_name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">last_name</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">username</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">avatar_id</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">dob</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">gender</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"7 characters\">company</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"17 characters\">is_public_profile</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"12 characters\">account_type</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">is_verified</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","whatsapp":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","telegram":"<pre class=sf-dump id=sf-dump-180432969 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">+33616581743</span>\"
</pre><script>Sfdump(\"sf-dump-180432969\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","commission":"<pre class=sf-dump id=sf-dump-1881091714 data-indent-pad=\"  \">\"\"
</pre><script>Sfdump(\"sf-dump-1881091714\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-1065080757 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">Students</span>\"
  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Professionals</span>\"
  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">For families</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1065080757\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","suitableLabel":"<pre class=sf-dump id=sf-dump-2096593398 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">For families</span>\"
</pre><script>Sfdump(\"sf-dump-2096593398\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-510761945 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-510761945\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","class":"<pre class=sf-dump id=sf-dump-1747207780 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"23 characters\">single-property-element</span>\"
</pre><script>Sfdump(\"sf-dump-1747207780\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined property: Illuminate\\Database\\Eloquent\\Relations\\MorphMany::$reviews_avg_star at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\partials\\project.blade.php:2)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#7 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\single-layouts\\style-5.blade.php(80): Illuminate\\View\\View->render()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#15 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\property.blade.php(52): Illuminate\\View\\View->render()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#23 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#24 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#26 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#27 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('long-term-rent-...', 'properties')
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'long-term-rent-...')
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#97 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined property: Illuminate\\Database\\Eloquent\\Relations\\MorphMany::$reviews_avg_star at D:\\laragon\\www\\xmetr\\storage\\framework\\views\\b49ecf3e3ae629e27a8ecad8858f1edd.php:2)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'D:\\\\laragon\\\\www\\\\...', 2)
#1 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\b49ecf3e3ae629e27a8ecad8858f1edd.php(2): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined prope...', 'D:\\\\laragon\\\\www\\\\...', 2)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\ad88605931b659db0874ab390e4ac9a6.php(81): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\03b54f071e37eb41b5e8510b59b8e693.php(53): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#25 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#26 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('real-estate.pro...', Array, 'plugins/real-es...')
#28 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#29 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(116): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('long-term-rent-...', 'properties')
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getViewWithPrefix('properties', 'long-term-rent-...')
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getViewWithPref...', Array)
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getViewWithPref...')
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#97 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#99 {main}
"} 
[2025-07-25 20:16:31] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 20:16:31] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:16:57] local.ERROR: Undefined variable $property {"view":{"view":"D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\projects\\item-grid.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-584009051 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3624</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-584009051\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","projects":"<pre class=sf-dump id=sf-dump-1543622069 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#3848</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3796</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3790</a> &#8230;30}
      <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3805</a> &#8230;30}
      <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3804</a> &#8230;30}
      <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3803</a> &#8230;30}
      <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3802</a> &#8230;30}
      <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3801</a> &#8230;30}
      <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3800</a> &#8230;30}
      <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3799</a> &#8230;30}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>20</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>8</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1543622069\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","actionUrl":"<pre class=sf-dump id=sf-dump-1577691860 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
</pre><script>Sfdump(\"sf-dump-1577691860\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","ajaxUrl":"<pre class=sf-dump id=sf-dump-508675094 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
</pre><script>Sfdump(\"sf-dump-508675094\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","mapUrl":"<pre class=sf-dump id=sf-dump-1834488279 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"37 characters\">https://xmetr.gc/en/ajax/projects/map</span>\"
</pre><script>Sfdump(\"sf-dump-1834488279\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","perPages":"<pre class=sf-dump id=sf-dump-1372229897 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-key>9</span> => <span class=sf-dump-num>9</span>
  <span class=sf-dump-key>12</span> => <span class=sf-dump-num>12</span>
  <span class=sf-dump-key>15</span> => <span class=sf-dump-num>15</span>
  <span class=sf-dump-key>30</span> => <span class=sf-dump-num>30</span>
  <span class=sf-dump-key>45</span> => <span class=sf-dump-num>45</span>
  <span class=sf-dump-key>60</span> => <span class=sf-dump-num>60</span>
  <span class=sf-dump-key>120</span> => <span class=sf-dump-num>120</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-1372229897\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","itemLayout":"<pre class=sf-dump id=sf-dump-804161234 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"4 characters\">grid</span>\"
</pre><script>Sfdump(\"sf-dump-804161234\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","layout":"<pre class=sf-dump id=sf-dump-1971318401 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"14 characters\">xmetr-projects</span>\"
</pre><script>Sfdump(\"sf-dump-1971318401\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","filterViewPath":"<pre class=sf-dump id=sf-dump-1885366379 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"66 characters\">theme.xmetr::views.real-estate.partials.filters.project-search-box</span>\"
</pre><script>Sfdump(\"sf-dump-1885366379\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","itemsViewPath":"<pre class=sf-dump id=sf-dump-1750344357 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"45 characters\">theme.xmetr::views.real-estate.projects.index</span>\"
</pre><script>Sfdump(\"sf-dump-1750344357\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","title":"<pre class=sf-dump id=sf-dump-1634981381 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"32 characters\">Catalog of Residential Complexes</span>\"
</pre><script>Sfdump(\"sf-dump-1634981381\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentFilters":"<pre class=sf-dump id=sf-dump-408552712 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-408552712\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hasExtraFilters":"<pre class=sf-dump id=sf-dump-779782381 data-indent-pad=\"  \"><span class=sf-dump-const>false</span>
</pre><script>Sfdump(\"sf-dump-779782381\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","countryName":"<pre class=sf-dump id=sf-dump-1167579375 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1167579375\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","cityName":"<pre class=sf-dump id=sf-dump-1909420701 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1909420701\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-625926875 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-625926875\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categoryName":"<pre class=sf-dump id=sf-dump-1719682001 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1719682001\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","bedrooms":"<pre class=sf-dump id=sf-dump-927398125 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-927398125\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","filteredQuery":"<pre class=sf-dump id=sf-dump-1084862482 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1084862482\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","buildClass":"<pre class=sf-dump id=sf-dump-1292781138 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\Collection</span> {<a class=sf-dump-ref>#3886</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>premium</span>\" => {<a class=sf-dump-ref>#3887</a><samp data-depth=3 class=sf-dump-compact>
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": \"<span class=sf-dump-str title=\"7 characters\">premium</span>\"
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">name</span>\": \"<span class=sf-dump-str title=\"7 characters\">Premium</span>\"
    </samp>}
    \"<span class=sf-dump-key>business</span>\" => {<a class=sf-dump-ref>#3870</a><samp data-depth=3 class=sf-dump-compact>
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": \"<span class=sf-dump-str title=\"8 characters\">business</span>\"
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">name</span>\": \"<span class=sf-dump-str title=\"8 characters\">Business</span>\"
    </samp>}
    \"<span class=sf-dump-key>standard</span>\" => {<a class=sf-dump-ref>#3884</a><samp data-depth=3 class=sf-dump-compact>
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": \"<span class=sf-dump-str title=\"8 characters\">standard</span>\"
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">name</span>\": \"<span class=sf-dump-str title=\"8 characters\">Standard</span>\"
    </samp>}
    \"<span class=sf-dump-key>economy</span>\" => {<a class=sf-dump-ref>#3883</a><samp data-depth=3 class=sf-dump-compact>
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": \"<span class=sf-dump-str title=\"7 characters\">economy</span>\"
      +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">name</span>\": \"<span class=sf-dump-str title=\"7 characters\">Economy</span>\"
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1292781138\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-1867293820 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#3848</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3796</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3790</a> &#8230;30}
      <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3805</a> &#8230;30}
      <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3804</a> &#8230;30}
      <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3803</a> &#8230;30}
      <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3802</a> &#8230;30}
      <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3801</a> &#8230;30}
      <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3800</a> &#8230;30}
      <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Project
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Project</span></span> {<a class=sf-dump-ref>#3799</a> &#8230;30}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>20</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"28 characters\">https://xmetr.gc/en/projects</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>8</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1867293820\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","buildClassItem":"<pre class=sf-dump id=sf-dump-800885994 data-indent-pad=\"  \">{<a class=sf-dump-ref>#3883</a><samp data-depth=1 class=sf-dump-expanded>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">id</span>\": \"<span class=sf-dump-str title=\"7 characters\">economy</span>\"
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">name</span>\": \"<span class=sf-dump-str title=\"7 characters\">Economy</span>\"
</samp>}
</pre><script>Sfdump(\"sf-dump-800885994\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-2077881258 data-indent-pad=\"  \">{<a class=sf-dump-ref>#3927</a><samp data-depth=1 class=sf-dump-expanded>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">iteration</span>\": <span class=sf-dump-num>1</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">index</span>\": <span class=sf-dump-num>0</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">remaining</span>\": <span class=sf-dump-num>7</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">count</span>\": <span class=sf-dump-num>8</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">first</span>\": <span class=sf-dump-const>true</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">last</span>\": <span class=sf-dump-const>false</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">odd</span>\": <span class=sf-dump-const>true</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">even</span>\": <span class=sf-dump-const>false</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">depth</span>\": <span class=sf-dump-num>1</span>
  +\"<span class=sf-dump-public title=\"Runtime added dynamic property\">parent</span>\": <span class=sf-dump-const>null</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-2077881258\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","country":"<pre class=sf-dump id=sf-dump-922501651 data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\Location\\Models\\Country</span> {<a class=sf-dump-ref>#3922</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">countries</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>199</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"
    \"<span class=sf-dump-key>nationality</span>\" => \"\"
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-19 00:16:15</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-20 21:24:11</span>\"
    \"<span class=sf-dump-key>code</span>\" => \"\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>199</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">United States</span>\"
    \"<span class=sf-dump-key>nationality</span>\" => \"\"
    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-01-19 00:16:15</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-05-20 21:24:11</span>\"
    \"<span class=sf-dump-key>code</span>\" => \"\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\Base\\Enums\\BaseStatusEnum</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>nationality</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>is_default</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bool</span>\"
    \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#3951</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">nationality</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">code</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">is_default</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-922501651\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","features":"<pre class=sf-dump id=sf-dump-325761862 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#3749</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3741</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3981</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3980</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3979</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3978</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3977</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3976</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3975</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3974</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3973</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>10</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3972</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    <span class=sf-dump-index>11</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\RealEstate\\Models\\Feature
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\RealEstate\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Feature</span></span> {<a class=sf-dump-ref>#3971</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-325761862\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","feature":"<pre class=sf-dump id=sf-dump-335029551 data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Feature</span> {<a class=sf-dump-ref>#3971</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_features</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#129385; Grill</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ti ti-grill</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">&#129385; Grill</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ti ti-grill</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>false</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">icon</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-335029551\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","itemsPerRow":"<pre class=sf-dump id=sf-dump-1647370979 data-indent-pad=\"  \"><span class=sf-dump-num>2</span>
</pre><script>Sfdump(\"sf-dump-1647370979\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","project":"<pre class=sf-dump id=sf-dump-274522825 data-indent-pad=\"  \"><span class=sf-dump-note>Xmetr\\RealEstate\\Models\\Project</span> {<a class=sf-dump-ref>#3790</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">re_projects</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:40</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Castle View</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"1286 characters\">&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &lt;strong&gt;Castle View&lt;/strong&gt;, &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1085;&#1099;&#1081; &#1087;&#1086; &#1072;&#1076;&#1088;&#1077;&#1089;&#1091; &lt;strong&gt;Luis Maria Campos 1000&lt;/strong&gt; &#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1077;, &#1087;&#1088;&#1077;&#1076;&#1089;&#1090;&#1072;&#1074;&#1083;&#1103;&#1077;&#1090; &#1089;&#1086;&#1073;&#1086;&#1081; &#1080;&#1076;&#1077;&#1072;&#1083;&#1100;&#1085;&#1086;&#1077; &#1089;&#1086;&#1095;&#1077;&#1090;&#1072;&#1085;&#1080;&#1077; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1086;&#1075;&#1086; &#1082;&#1086;&#1084;&#1092;&#1086;&#1088;&#1090;&#1072; &#1080; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1075;&#1086; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1080;&#1103; &#1074; &#1087;&#1088;&#1077;&#1089;&#1090;&#1080;&#1078;&#1085;&#1086;&#1084; &#1088;&#1072;&#1081;&#1086;&#1085;&#1077; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1047;&#1076;&#1072;&#1085;&#1080;&#1077; &#1086;&#1082;&#1088;&#1091;&#1078;&#1077;&#1085;&#1086; &#1082;&#1088;&#1072;&#1089;&#1080;&#1074;&#1099;&#1084;&#1080; &#1079;&#1077;&#1083;&#1077;&#1085;&#1099;&#1084;&#1080; &#1087;&#1072;&#1088;&#1082;&#1072;&#1084;&#1080;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &lt;strong&gt;Parque Las Heras&lt;/strong&gt;, &#1082;&#1086;&#1090;&#1086;&#1088;&#1099;&#1081; &#1085;&#1072;&#1093;&#1086;&#1076;&#1080;&#1090;&#1089;&#1103; &#1074;&#1089;&#1077;&#1075;&#1086; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1084;&#1080;&#1085;&#1091;&#1090;&#1072;&#1093; &#1093;&#1086;&#1076;&#1100;&#1073;&#1099;. &#1069;&#1090;&#1086; &#1086;&#1090;&#1083;&#1080;&#1095;&#1085;&#1086;&#1077; &#1084;&#1077;&#1089;&#1090;&#1086; &#1076;&#1083;&#1103; &#1087;&#1088;&#1086;&#1075;&#1091;&#1083;&#1086;&#1082;, &#1079;&#1072;&#1085;&#1103;&#1090;&#1080;&#1081; &#1089;&#1087;&#1086;&#1088;&#1090;&#1086;&#1084; &#1080; &#1086;&#1090;&#1076;&#1099;&#1093;&#1072; &#1085;&#1072; &#1089;&#1074;&#1077;&#1078;&#1077;&#1084; &#1074;&#1086;&#1079;&#1076;&#1091;&#1093;&#1077;.&lt;/p&gt;&lt;p&gt;&#1042;&#1073;&#1083;&#1080;&#1079;&#1080; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;&#1072; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1099; &lt;strong&gt;&#1089;&#1091;&#1087;&#1077;&#1088;&#1084;&#1072;&#1088;&#1082;&#1077;&#1090;&#1099;, &#1084;&#1072;&#1075;&#1072;&#1079;&#1080;&#1085;&#1099;, &#1082;&#1072;&#1092;&#1077; &#1080; &#1088;&#1077;&#1089;&#1090;&#1086;&#1088;&#1072;&#1085;&#1099;&lt;/strong&gt;, &#1095;&#1090;&#1086; &#1076;&#1077;&#1083;&#1072;&#1077;&#1090; &#1087;&#1086;&#1074;&#1089;&#1077;&#1076;&#1085;&#1077;&#1074;&#1085;&#1091;&#1102; &#1078;&#1080;&#1079;&#1085;&#1100; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1081; &#1080; &#1085;&#1072;&#1089;&#1099;&#1097;&#1077;&#1085;&#1085;&#1086;&#1081;. &#1058;&#1072;&#1082;&#1078;&#1077; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1082;&#1074;&#1072;&#1088;&#1090;&#1072;&#1083;&#1072;&#1093; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &lt;strong&gt;&#1086;&#1073;&#1088;&#1072;&#1079;&#1086;&#1074;&#1072;&#1090;&#1077;&#1083;&#1100;&#1085;&#1099;&#1077; &#1091;&#1095;&#1088;&#1077;&#1078;&#1076;&#1077;&#1085;&#1080;&#1103;&lt;/strong&gt;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &#1096;&#1082;&#1086;&#1083;&#1099; &#1080; &#1076;&#1077;&#1090;&#1089;&#1082;&#1080;&#1077; &#1089;&#1072;&#1076;&#1099;, &#1095;&#1090;&#1086; &#1086;&#1089;&#1086;&#1073;&#1077;&#1085;&#1085;&#1086; &#1074;&#1072;&#1078;&#1085;&#1086; &#1076;&#1083;&#1103; &#1089;&#1077;&#1084;&#1077;&#1081; &#1089; &#1076;&#1077;&#1090;&#1100;&#1084;&#1080;.&lt;/p&gt;&lt;p&gt;&#1041;&#1083;&#1072;&#1075;&#1086;&#1076;&#1072;&#1088;&#1103; &#1073;&#1083;&#1080;&#1079;&#1086;&#1089;&#1090;&#1080; &#1082; &#1075;&#1083;&#1072;&#1074;&#1085;&#1099;&#1084; &#1090;&#1088;&#1072;&#1085;&#1089;&#1087;&#1086;&#1088;&#1090;&#1085;&#1099;&#1084; &#1091;&#1079;&#1083;&#1072;&#1084;, &lt;strong&gt;Castle View&lt;/strong&gt; &#1086;&#1073;&#1077;&#1089;&#1087;&#1077;&#1095;&#1080;&#1074;&#1072;&#1077;&#1090; &#1083;&#1077;&#1075;&#1082;&#1080;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087; &#1082; &#1088;&#1072;&#1079;&#1083;&#1080;&#1095;&#1085;&#1099;&#1084; &#1095;&#1072;&#1089;&#1090;&#1103;&#1084; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1042; &#1087;&#1077;&#1096;&#1077;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087;&#1085;&#1086;&#1089;&#1090;&#1080; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &#1072;&#1074;&#1090;&#1086;&#1073;&#1091;&#1089;&#1085;&#1099;&#1077; &#1086;&#1089;&#1090;&#1072;&#1085;&#1086;&#1074;&#1082;&#1080; &#1080; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103; &#1084;&#1077;&#1090;&#1088;&#1086;, &#1072; &#1090;&#1072;&#1082;&#1078;&#1077; &lt;strong&gt;&#1078;&#1077;&#1083;&#1077;&#1079;&#1085;&#1086;&#1076;&#1086;&#1088;&#1086;&#1078;&#1085;&#1072;&#1103; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103;&lt;/strong&gt; Belgrano C, &#1095;&#1090;&#1086; &#1087;&#1086;&#1079;&#1074;&#1086;&#1083;&#1103;&#1077;&#1090; &#1073;&#1099;&#1089;&#1090;&#1088;&#1086; &#1076;&#1086;&#1073;&#1088;&#1072;&#1090;&#1100;&#1089;&#1103; &#1076;&#1086; &#1076;&#1077;&#1083;&#1086;&#1074;&#1086;&#1075;&#1086; &#1094;&#1077;&#1085;&#1090;&#1088;&#1072; &#1080;&#1083;&#1080; &#1076;&#1088;&#1091;&#1075;&#1080;&#1093; &#1088;&#1072;&#1081;&#1086;&#1085;&#1086;&#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1072;.&lt;/p&gt;&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &#1090;&#1072;&#1082;&#1078;&#1077; &#1087;&#1088;&#1077;&#1076;&#1083;&#1072;&#1075;&#1072;&#1077;&#1090; &lt;strong&gt;&#1074;&#1099;&#1089;&#1086;&#1082;&#1080;&#1081; &#1091;&#1088;&#1086;&#1074;&#1077;&#1085;&#1100; &#1073;&#1077;&#1079;&#1086;&#1087;&#1072;&#1089;&#1085;&#1086;&#1089;&#1090;&#1080;&lt;/strong&gt; &#1080; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1091;&#1102; &#1080;&#1085;&#1092;&#1088;&#1072;&#1089;&#1090;&#1088;&#1091;&#1082;&#1090;&#1091;&#1088;&#1091;: &#1082;&#1088;&#1091;&#1075;&#1083;&#1086;&#1089;&#1091;&#1090;&#1086;&#1095;&#1085;&#1091;&#1102; &#1086;&#1093;&#1088;&#1072;&#1085;&#1091;, &#1087;&#1072;&#1088;&#1082;&#1086;&#1074;&#1082;&#1091;, &#1090;&#1088;&#1077;&#1085;&#1072;&#1078;&#1077;&#1088;&#1085;&#1099;&#1081; &#1079;&#1072;&#1083; &#1080; &#1073;&#1072;&#1089;&#1089;&#1077;&#1081;&#1085;.&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"833 characters\">{&quot;1&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-1.jpg&quot;,&quot;2&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-9.jpg&quot;,&quot;3&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-7.jpg&quot;,&quot;4&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-3.jpg&quot;,&quot;5&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-10.jpg&quot;,&quot;6&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-11-1.jpg&quot;,&quot;7&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-8-1.jpg&quot;,&quot;8&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-6-1.jpg&quot;,&quot;9&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-4-1.jpg&quot;,&quot;10&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-5-1.jpg&quot;,&quot;11&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-12.jpg&quot;,&quot;12&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-2.jpg&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => \"<span class=sf-dump-str title=\"29 characters\">accounts/m-229/testvideo1.mp4</span>\"
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Luis Maria Campos 1000</span>\"
    \"<span class=sf-dump-key>investor_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_block</span>\" => <span class=sf-dump-num>20</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-num>30</span>
    \"<span class=sf-dump-key>number_flat</span>\" => <span class=sf-dump-num>140</span>
    \"<span class=sf-dump-key>parking</span>\" => <span class=sf-dump-num>120</span>
    \"<span class=sf-dump-key>year_built</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2020</span>\"
    \"<span class=sf-dump-key>build_class</span>\" => \"<span class=sf-dump-str title=\"8 characters\">business</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>date_finish</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>date_sell</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>price_from</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>price_to</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8204</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-num>38</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>167</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-17 17:41:23</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-21 23:34:34</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5710205</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-58.4373146</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>207</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => \"\"
    \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>reviews_avg_star</span>\" => \"<span class=sf-dump-str title=\"6 characters\">4.0000</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:40</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Castle View</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"1286 characters\">&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &lt;strong&gt;Castle View&lt;/strong&gt;, &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1085;&#1099;&#1081; &#1087;&#1086; &#1072;&#1076;&#1088;&#1077;&#1089;&#1091; &lt;strong&gt;Luis Maria Campos 1000&lt;/strong&gt; &#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1077;, &#1087;&#1088;&#1077;&#1076;&#1089;&#1090;&#1072;&#1074;&#1083;&#1103;&#1077;&#1090; &#1089;&#1086;&#1073;&#1086;&#1081; &#1080;&#1076;&#1077;&#1072;&#1083;&#1100;&#1085;&#1086;&#1077; &#1089;&#1086;&#1095;&#1077;&#1090;&#1072;&#1085;&#1080;&#1077; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1086;&#1075;&#1086; &#1082;&#1086;&#1084;&#1092;&#1086;&#1088;&#1090;&#1072; &#1080; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1075;&#1086; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1080;&#1103; &#1074; &#1087;&#1088;&#1077;&#1089;&#1090;&#1080;&#1078;&#1085;&#1086;&#1084; &#1088;&#1072;&#1081;&#1086;&#1085;&#1077; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1047;&#1076;&#1072;&#1085;&#1080;&#1077; &#1086;&#1082;&#1088;&#1091;&#1078;&#1077;&#1085;&#1086; &#1082;&#1088;&#1072;&#1089;&#1080;&#1074;&#1099;&#1084;&#1080; &#1079;&#1077;&#1083;&#1077;&#1085;&#1099;&#1084;&#1080; &#1087;&#1072;&#1088;&#1082;&#1072;&#1084;&#1080;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &lt;strong&gt;Parque Las Heras&lt;/strong&gt;, &#1082;&#1086;&#1090;&#1086;&#1088;&#1099;&#1081; &#1085;&#1072;&#1093;&#1086;&#1076;&#1080;&#1090;&#1089;&#1103; &#1074;&#1089;&#1077;&#1075;&#1086; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1084;&#1080;&#1085;&#1091;&#1090;&#1072;&#1093; &#1093;&#1086;&#1076;&#1100;&#1073;&#1099;. &#1069;&#1090;&#1086; &#1086;&#1090;&#1083;&#1080;&#1095;&#1085;&#1086;&#1077; &#1084;&#1077;&#1089;&#1090;&#1086; &#1076;&#1083;&#1103; &#1087;&#1088;&#1086;&#1075;&#1091;&#1083;&#1086;&#1082;, &#1079;&#1072;&#1085;&#1103;&#1090;&#1080;&#1081; &#1089;&#1087;&#1086;&#1088;&#1090;&#1086;&#1084; &#1080; &#1086;&#1090;&#1076;&#1099;&#1093;&#1072; &#1085;&#1072; &#1089;&#1074;&#1077;&#1078;&#1077;&#1084; &#1074;&#1086;&#1079;&#1076;&#1091;&#1093;&#1077;.&lt;/p&gt;&lt;p&gt;&#1042;&#1073;&#1083;&#1080;&#1079;&#1080; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089;&#1072; &#1088;&#1072;&#1089;&#1087;&#1086;&#1083;&#1086;&#1078;&#1077;&#1085;&#1099; &lt;strong&gt;&#1089;&#1091;&#1087;&#1077;&#1088;&#1084;&#1072;&#1088;&#1082;&#1077;&#1090;&#1099;, &#1084;&#1072;&#1075;&#1072;&#1079;&#1080;&#1085;&#1099;, &#1082;&#1072;&#1092;&#1077; &#1080; &#1088;&#1077;&#1089;&#1090;&#1086;&#1088;&#1072;&#1085;&#1099;&lt;/strong&gt;, &#1095;&#1090;&#1086; &#1076;&#1077;&#1083;&#1072;&#1077;&#1090; &#1087;&#1086;&#1074;&#1089;&#1077;&#1076;&#1085;&#1077;&#1074;&#1085;&#1091;&#1102; &#1078;&#1080;&#1079;&#1085;&#1100; &#1091;&#1076;&#1086;&#1073;&#1085;&#1086;&#1081; &#1080; &#1085;&#1072;&#1089;&#1099;&#1097;&#1077;&#1085;&#1085;&#1086;&#1081;. &#1058;&#1072;&#1082;&#1078;&#1077; &#1074; &#1085;&#1077;&#1089;&#1082;&#1086;&#1083;&#1100;&#1082;&#1080;&#1093; &#1082;&#1074;&#1072;&#1088;&#1090;&#1072;&#1083;&#1072;&#1093; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &lt;strong&gt;&#1086;&#1073;&#1088;&#1072;&#1079;&#1086;&#1074;&#1072;&#1090;&#1077;&#1083;&#1100;&#1085;&#1099;&#1077; &#1091;&#1095;&#1088;&#1077;&#1078;&#1076;&#1077;&#1085;&#1080;&#1103;&lt;/strong&gt;, &#1074;&#1082;&#1083;&#1102;&#1095;&#1072;&#1103; &#1096;&#1082;&#1086;&#1083;&#1099; &#1080; &#1076;&#1077;&#1090;&#1089;&#1082;&#1080;&#1077; &#1089;&#1072;&#1076;&#1099;, &#1095;&#1090;&#1086; &#1086;&#1089;&#1086;&#1073;&#1077;&#1085;&#1085;&#1086; &#1074;&#1072;&#1078;&#1085;&#1086; &#1076;&#1083;&#1103; &#1089;&#1077;&#1084;&#1077;&#1081; &#1089; &#1076;&#1077;&#1090;&#1100;&#1084;&#1080;.&lt;/p&gt;&lt;p&gt;&#1041;&#1083;&#1072;&#1075;&#1086;&#1076;&#1072;&#1088;&#1103; &#1073;&#1083;&#1080;&#1079;&#1086;&#1089;&#1090;&#1080; &#1082; &#1075;&#1083;&#1072;&#1074;&#1085;&#1099;&#1084; &#1090;&#1088;&#1072;&#1085;&#1089;&#1087;&#1086;&#1088;&#1090;&#1085;&#1099;&#1084; &#1091;&#1079;&#1083;&#1072;&#1084;, &lt;strong&gt;Castle View&lt;/strong&gt; &#1086;&#1073;&#1077;&#1089;&#1087;&#1077;&#1095;&#1080;&#1074;&#1072;&#1077;&#1090; &#1083;&#1077;&#1075;&#1082;&#1080;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087; &#1082; &#1088;&#1072;&#1079;&#1083;&#1080;&#1095;&#1085;&#1099;&#1084; &#1095;&#1072;&#1089;&#1090;&#1103;&#1084; &#1075;&#1086;&#1088;&#1086;&#1076;&#1072;. &#1042; &#1087;&#1077;&#1096;&#1077;&#1081; &#1076;&#1086;&#1089;&#1090;&#1091;&#1087;&#1085;&#1086;&#1089;&#1090;&#1080; &#1085;&#1072;&#1093;&#1086;&#1076;&#1103;&#1090;&#1089;&#1103; &#1072;&#1074;&#1090;&#1086;&#1073;&#1091;&#1089;&#1085;&#1099;&#1077; &#1086;&#1089;&#1090;&#1072;&#1085;&#1086;&#1074;&#1082;&#1080; &#1080; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103; &#1084;&#1077;&#1090;&#1088;&#1086;, &#1072; &#1090;&#1072;&#1082;&#1078;&#1077; &lt;strong&gt;&#1078;&#1077;&#1083;&#1077;&#1079;&#1085;&#1086;&#1076;&#1086;&#1088;&#1086;&#1078;&#1085;&#1072;&#1103; &#1089;&#1090;&#1072;&#1085;&#1094;&#1080;&#1103;&lt;/strong&gt; Belgrano C, &#1095;&#1090;&#1086; &#1087;&#1086;&#1079;&#1074;&#1086;&#1083;&#1103;&#1077;&#1090; &#1073;&#1099;&#1089;&#1090;&#1088;&#1086; &#1076;&#1086;&#1073;&#1088;&#1072;&#1090;&#1100;&#1089;&#1103; &#1076;&#1086; &#1076;&#1077;&#1083;&#1086;&#1074;&#1086;&#1075;&#1086; &#1094;&#1077;&#1085;&#1090;&#1088;&#1072; &#1080;&#1083;&#1080; &#1076;&#1088;&#1091;&#1075;&#1080;&#1093; &#1088;&#1072;&#1081;&#1086;&#1085;&#1086;&#1074; &#1041;&#1091;&#1101;&#1085;&#1086;&#1089;-&#1040;&#1081;&#1088;&#1077;&#1089;&#1072;.&lt;/p&gt;&lt;p&gt;&#1046;&#1080;&#1083;&#1086;&#1081; &#1082;&#1086;&#1084;&#1087;&#1083;&#1077;&#1082;&#1089; &#1090;&#1072;&#1082;&#1078;&#1077; &#1087;&#1088;&#1077;&#1076;&#1083;&#1072;&#1075;&#1072;&#1077;&#1090; &lt;strong&gt;&#1074;&#1099;&#1089;&#1086;&#1082;&#1080;&#1081; &#1091;&#1088;&#1086;&#1074;&#1077;&#1085;&#1100; &#1073;&#1077;&#1079;&#1086;&#1087;&#1072;&#1089;&#1085;&#1086;&#1089;&#1090;&#1080;&lt;/strong&gt; &#1080; &#1089;&#1086;&#1074;&#1088;&#1077;&#1084;&#1077;&#1085;&#1085;&#1091;&#1102; &#1080;&#1085;&#1092;&#1088;&#1072;&#1089;&#1090;&#1088;&#1091;&#1082;&#1090;&#1091;&#1088;&#1091;: &#1082;&#1088;&#1091;&#1075;&#1083;&#1086;&#1089;&#1091;&#1090;&#1086;&#1095;&#1085;&#1091;&#1102; &#1086;&#1093;&#1088;&#1072;&#1085;&#1091;, &#1087;&#1072;&#1088;&#1082;&#1086;&#1074;&#1082;&#1091;, &#1090;&#1088;&#1077;&#1085;&#1072;&#1078;&#1077;&#1088;&#1085;&#1099;&#1081; &#1079;&#1072;&#1083; &#1080; &#1073;&#1072;&#1089;&#1089;&#1077;&#1081;&#1085;.&lt;/p&gt;</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"833 characters\">{&quot;1&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-1.jpg&quot;,&quot;2&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-9.jpg&quot;,&quot;3&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-7.jpg&quot;,&quot;4&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-3.jpg&quot;,&quot;5&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-10.jpg&quot;,&quot;6&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-11-1.jpg&quot;,&quot;7&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-8-1.jpg&quot;,&quot;8&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-6-1.jpg&quot;,&quot;9&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-4-1.jpg&quot;,&quot;10&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-5-1.jpg&quot;,&quot;11&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-12.jpg&quot;,&quot;12&quot;:&quot;properties\\/gk-castle-view\\/gk-buenos-aires-castle-view-2.jpg&quot;}</span>\"
    \"<span class=sf-dump-key>video</span>\" => \"<span class=sf-dump-str title=\"29 characters\">accounts/m-229/testvideo1.mp4</span>\"
    \"<span class=sf-dump-key>video_thumbnail</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Luis Maria Campos 1000</span>\"
    \"<span class=sf-dump-key>investor_id</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>number_block</span>\" => <span class=sf-dump-num>20</span>
    \"<span class=sf-dump-key>number_floor</span>\" => <span class=sf-dump-num>30</span>
    \"<span class=sf-dump-key>number_flat</span>\" => <span class=sf-dump-num>140</span>
    \"<span class=sf-dump-key>parking</span>\" => <span class=sf-dump-num>120</span>
    \"<span class=sf-dump-key>year_built</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2020</span>\"
    \"<span class=sf-dump-key>build_class</span>\" => \"<span class=sf-dump-str title=\"8 characters\">business</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>date_finish</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>date_sell</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>price_from</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>price_to</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>city_id</span>\" => <span class=sf-dump-num>8204</span>
    \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-num>38</span>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"
    \"<span class=sf-dump-key>author_id</span>\" => <span class=sf-dump-num>167</span>
    \"<span class=sf-dump-key>author_type</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Xmetr\\RealEstate\\Models\\Account</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-10-17 17:41:23</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-21 23:34:34</span>\"
    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-34.5710205</span>\"
    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"11 characters\">-58.4373146</span>\"
    \"<span class=sf-dump-key>views</span>\" => <span class=sf-dump-num>207</span>
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>telegram_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>phone_clicks</span>\" => <span class=sf-dump-num>0</span>
    \"<span class=sf-dump-key>country_id</span>\" => <span class=sf-dump-num>29</span>
    \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>unique_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>private_notes</span>\" => \"\"
    \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>reviews_avg_star</span>\" => \"<span class=sf-dump-str title=\"6 characters\">4.0000</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:22</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xmetr\\RealEstate\\Enums\\ProjectStatusEnum</span>\"
    \"<span class=sf-dump-key>date_finish</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>date_sell</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>price_from</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>price_to</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"
    \"<span class=sf-dump-key>number_block</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_float</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>number_flat</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>views</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>private_notes</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Xmetr\\Base\\Casts\\SafeContent</span>\"
    \"<span class=sf-dump-key>images</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"
    \"<span class=sf-dump-key>parking</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>year_built</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>build_class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Xmetr\\RealEstate\\Enums\\ProjectBuildClassEnum</span>\"
    \"<span class=sf-dump-key>whatsapp_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>telegram_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
    \"<span class=sf-dump-key>phone_clicks</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>slugable</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Slug\\Models\\Slug
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Slug\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Slug</span></span> {<a class=sf-dump-ref>#3815</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">slugs</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>state</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\State
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">State</span></span> {<a class=sf-dump-ref>#3755</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">states</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>false</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [ &#8230;5]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>city</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Xmetr\\Location\\Models\\City
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Xmetr\\Location\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">City</span></span> {<a class=sf-dump-ref>#3855</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">cities</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
    </samp>}
    \"<span class=sf-dump-key>categories</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3797</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:35</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">content</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">images</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">video</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"15 characters\">video_thumbnail</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"11 characters\">investor_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"12 characters\">number_block</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">number_floor</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"11 characters\">number_flat</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">date_finish</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"9 characters\">date_sell</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">price_from</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"8 characters\">price_to</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">currency_id</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"7 characters\">city_id</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"11 characters\">district_id</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"8 characters\">state_id</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">country_id</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"9 characters\">author_id</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"11 characters\">author_type</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"8 characters\">latitude</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"9 characters\">longitude</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"9 characters\">unique_id</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"13 characters\">private_notes</span>\"
    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"7 characters\">parking</span>\"
    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"10 characters\">year_built</span>\"
    <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"11 characters\">build_class</span>\"
    <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">whatsapp_clicks</span>\"
    <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"15 characters\">telegram_clicks</span>\"
    <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"12 characters\">phone_clicks</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-274522825\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $property at D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\projects\\item-grid.blade.php:85)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#7 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\projects\\grid.blade.php(4): Illuminate\\View\\View->render()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#15 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\projects\\index.blade.php(17): Illuminate\\View\\View->render()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#23 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\listing-layouts\\xmetr-projects.blade.php(432): Illuminate\\View\\View->render()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#25 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#29 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#31 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\partials\\listing.blade.php(1): Illuminate\\View\\View->render()
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#37 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#39 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\real-estate\\projects.blade.php(4): Illuminate\\View\\View->render()
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#45 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#47 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(661): Illuminate\\View\\View->render()
#48 [internal function]: Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}('<div class=\"ck-...', Object(Xmetr\\Page\\Models\\Page))
#49 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Object(Closure), Array)
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Base\\Supports\\Filter->fire('page_front_page...', Array)
#51 D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#52 D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\views\\page.blade.php(8): apply_filters('page_front_page...', '<div class=\"ck-...', Object(Xmetr\\Page\\Models\\Page))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#58 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#60 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#61 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('page', Array, 'packages/page::...')
#63 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('projects', '')
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getView', Array)
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getView')
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#99 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#100 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#101 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#106 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#107 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#108 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#109 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#111 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#118 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#119 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#120 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#121 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#122 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#123 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#124 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#127 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#128 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#129 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#130 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#131 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#132 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#133 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $property at D:\\laragon\\www\\xmetr\\storage\\framework\\views\\b8cd76a6b0de5ffe6cc154657b47d195.php:21)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 21)
#1 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\b8cd76a6b0de5ffe6cc154657b47d195.php(21): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'D:\\\\laragon\\\\www\\\\...', 21)
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\8d929a4f363e0bfdd81af98edf3298b9.php(4): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\836163938ffdc64085e583762b49515c.php(17): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#25 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\ceb55cfe2aff9881146b823749a4838b.php(401): Illuminate\\View\\View->render()
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#27 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#30 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#32 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#33 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\62cd0299d8879989429d1d66a10d02c8.php(1): Illuminate\\View\\View->render()
#34 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#36 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#38 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#39 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#40 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#41 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\f8542d08948b0220b1590bc945559008.php(14): Illuminate\\View\\View->render()
#42 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#46 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#47 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#49 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php(661): Illuminate\\View\\View->render()
#50 [internal function]: Xmetr\\RealEstate\\Providers\\HookServiceProvider->Xmetr\\RealEstate\\Providers\\{closure}('<div class=\"ck-...', Object(Xmetr\\Page\\Models\\Page))
#51 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Object(Closure), Array)
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Base\\Supports\\Filter->fire('page_front_page...', Array)
#53 D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#54 D:\\laragon\\www\\xmetr\\storage\\framework\\views\\94c231b1899c59d10ec8a454d64195ef.php(8): apply_filters('page_front_page...', '<div class=\"ck-...', Object(Xmetr\\Page\\Models\\Page))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#60 D:\\laragon\\www\\xmetr\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Xmetr\\Shortcode\\View\\View->renderContents()
#62 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(682): Illuminate\\View\\View->render()
#63 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Theme.php(661): Xmetr\\Theme\\Theme->setUpContent('theme.xmetr::vi...', Array)
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Xmetr\\Theme\\Theme->scope('page', Array, 'packages/page::...')
#65 D:\\laragon\\www\\xmetr\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(86): Illuminate\\Support\\Facades\\Facade::__callStatic('scope', Array)
#66 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Xmetr\\Theme\\Http\\Controllers\\PublicController->getView('projects', '')
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getView', Array)
#68 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\Theme\\Http\\Controllers\\PublicController), 'getView')
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#101 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#102 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#103 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#107 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#108 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#109 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#110 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#111 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#113 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#120 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#121 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#122 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#123 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#124 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#125 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#126 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#127 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#128 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#129 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#130 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#131 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#132 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#133 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#134 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#135 {main}
"} 
[2025-07-25 20:17:28] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 20:17:33] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:17:33] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 20:28:54] local.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Acc...', false)
#2 D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Account c...', true)
#4 D:\\laragon\\www\\xmetr\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Account c...', true)
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Account c...')
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laragon\\www\\xmetr\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\xmetr\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-07-25 20:43:12] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 21:22:18] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\ProjectBuildClassEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:22:18] local.ERROR: Value "building" is not part of the enum Xmetr\RealEstate\Enums\ProjectStatusEnum  
[2025-07-25 21:38:57] local.ERROR: Method Illuminate\Validation\Validator::validateAny does not exist. {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Validation\\Validator::validateAny does not exist. at D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php:1646)
[stacktrace]
#0 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(660): Illuminate\\Validation\\Validator->__call('validateAny', Array)
#1 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('images', 'Any')
#2 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(494): Illuminate\\Validation\\Validator->passes()
#3 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#4 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#5 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1302): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest), Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1266): Illuminate\\Container\\Container->fireCallbackArray(Object(Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest), Array)
#7 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1252): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('Xmetr\\\\RealEstat...', Object(Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest))
#8 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(813): Illuminate\\Container\\Container->fireResolvingCallbacks('Xmetr\\\\RealEstat...', Object(Xmetr\\RealEstate\\Http\\Requests\\AccountPropertyRequest))
#9 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Xmetr\\\\RealEstat...', Array, true)
#10 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Xmetr\\\\RealEstat...', Array)
#11 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Xmetr\\\\RealEstat...', Array)
#12 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(85): Illuminate\\Foundation\\Application->make('Xmetr\\\\RealEstat...')
#13 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(50): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#14 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(29): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#15 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController), 'store')
#16 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController), 'store')
#17 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController), 'store')
#18 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#20 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\EnsureAccountIsApproved.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->Xmetr\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\xmetr\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\xmetr\\app\\Http\\Middleware\\RedirectIncorrectLanguagePrefix.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIncorrectLanguagePrefix->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\RedirectIfNotAccount.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\RealEstate\\Http\\Middleware\\RedirectIfNotAccount->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\xmetr\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Xmetr\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\xmetr\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#86 D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\xmetr\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#88 {main}
"} 
[2025-07-25 21:44:00] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\PropertyStatusEnum  
[2025-07-25 21:44:11] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\PropertyStatusEnum  
[2025-07-25 21:44:46] local.ERROR: Value "" is not part of the enum Xmetr\RealEstate\Enums\PropertyStatusEnum  
