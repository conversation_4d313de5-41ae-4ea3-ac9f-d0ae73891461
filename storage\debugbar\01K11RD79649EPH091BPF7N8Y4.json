{"__meta": {"id": "01K11RD79649EPH091BPF7N8Y4", "datetime": "2025-07-25 21:39:12", "utime": ********52.295727, "method": "POST", "uri": "/en/account/properties/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": ********50.922378, "end": ********52.295739, "duration": 1.****************, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": ********50.922378, "relative_start": 0, "end": **********.670848, "relative_end": **********.670848, "duration": 0.****************, "duration_str": "748ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.67086, "relative_start": 0.****************, "end": ********52.295741, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "625ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.690016, "relative_start": 0.****************, "end": **********.708199, "relative_end": **********.708199, "duration": 0.018182992935180664, "duration_str": "18.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/real-estate::partials.form-features", "start": **********.844988, "relative_start": 0.****************, "end": **********.844988, "relative_end": **********.844988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.849005, "relative_start": 0.****************, "end": **********.849005, "relative_end": **********.849005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.851272, "relative_start": 0.92889404296875, "end": **********.851272, "relative_end": **********.851272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.853103, "relative_start": 0.9307248592376709, "end": **********.853103, "relative_end": **********.853103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.854913, "relative_start": 0.93253493309021, "end": **********.854913, "relative_end": **********.854913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.856898, "relative_start": 0.9345200061798096, "end": **********.856898, "relative_end": **********.856898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.85903, "relative_start": 0.9366519451141357, "end": **********.85903, "relative_end": **********.85903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.86084, "relative_start": 0.9384620189666748, "end": **********.86084, "relative_end": **********.86084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.862644, "relative_start": 0.9402658939361572, "end": **********.862644, "relative_end": **********.862644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.86509, "relative_start": 0.9427118301391602, "end": **********.86509, "relative_end": **********.86509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.867269, "relative_start": 0.9448909759521484, "end": **********.867269, "relative_end": **********.867269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.86914, "relative_start": 0.9467618465423584, "end": **********.86914, "relative_end": **********.86914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.870936, "relative_start": 0.9485578536987305, "end": **********.870936, "relative_end": **********.870936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.872898, "relative_start": 0.9505200386047363, "end": **********.872898, "relative_end": **********.872898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.874708, "relative_start": 0.9523298740386963, "end": **********.874708, "relative_end": **********.874708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.87652, "relative_start": 0.9541418552398682, "end": **********.87652, "relative_end": **********.87652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.878325, "relative_start": 0.9559469223022461, "end": **********.878325, "relative_end": **********.878325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.880132, "relative_start": 0.9577538967132568, "end": **********.880132, "relative_end": **********.880132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.882012, "relative_start": 0.9596338272094727, "end": **********.882012, "relative_end": **********.882012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.884028, "relative_start": 0.9616498947143555, "end": **********.884028, "relative_end": **********.884028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.885853, "relative_start": 0.9634749889373779, "end": **********.885853, "relative_end": **********.885853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.887695, "relative_start": 0.9653170108795166, "end": **********.887695, "relative_end": **********.887695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/real-estate::partials.form-suitable", "start": **********.888649, "relative_start": 0.966270923614502, "end": **********.888649, "relative_end": **********.888649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.889108, "relative_start": 0.9667298793792725, "end": **********.889108, "relative_end": **********.889108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.889735, "relative_start": 0.9673569202423096, "end": **********.889735, "relative_end": **********.889735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.890345, "relative_start": 0.9679670333862305, "end": **********.890345, "relative_end": **********.890345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": ********52.28975, "relative_start": 1.3673720359802246, "end": ********52.293468, "relative_end": ********52.293468, "duration": 0.0037178993225097656, "duration_str": "3.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 47230544, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 26, "nb_templates": 26, "templates": [{"name": "plugins/real-estate::partials.form-features", "param_count": null, "params": [], "start": **********.844963, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-features.blade.phpplugins/real-estate::partials.form-features", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-features.blade.php:1", "ajax": false, "filename": "form-features.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.848983, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.851254, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.853086, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.854896, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.856856, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.859012, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.860822, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.862626, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.865072, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.867249, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.869122, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.870919, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.87288, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.87469, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.876502, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.878308, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.880115, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.881994, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.884009, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.885835, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.887678, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "plugins/real-estate::partials.form-suitable", "param_count": null, "params": [], "start": **********.888631, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/real-estate/resources/views/partials/form-suitable.blade.phpplugins/real-estate::partials.form-suitable", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fresources%2Fviews%2Fpartials%2Fform-suitable.blade.php:1", "ajax": false, "filename": "form-suitable.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.889091, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.889718, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "********************************::form.checkbox", "param_count": null, "params": [], "start": **********.890328, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php:1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}]}, "queries": {"count": 40, "nb_statements": 40, "nb_visible_statements": 40, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "213ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `re_accounts` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "account", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\RedirectIfNotAccount.php", "line": 16}], "start": **********.722212, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.235}, {"sql": "select `name`, `id` from `re_projects` order by `created_at` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 71}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 22}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.7496428, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.235, "width_percent": 0.197}, {"sql": "select `title`, `id` from `re_currencies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 22}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}], "start": **********.756747, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PropertyForm.php:75", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 75}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FForms%2FPropertyForm.php:75", "ajax": false, "filename": "PropertyForm.php", "line": "75"}, "connection": "xmetr", "explain": null, "start_percent": 0.432, "width_percent": 0.141}, {"sql": "select `id`, `name` from `re_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 99}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 22}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.7753391, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.573, "width_percent": 0.225}, {"sql": "select `id`, `name` from `re_facilities`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/PropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\PropertyForm.php", "line": 123}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 22}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 19, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}], "start": **********.795829, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.798, "width_percent": 0.221}, {"sql": "select `id`, `name`, `parent_id` from `re_categories` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 126}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/helpers/helpers.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\helpers\\helpers.php", "line": 38}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Forms/AccountPropertyForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Forms\\AccountPropertyForm.php", "line": 22}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}], "start": **********.89406, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 1.019, "width_percent": 0.183}, {"sql": "insert into `re_properties` (`type`, `period`, `rental_period`, `status`, `images`, `original_description`, `country_id`, `state_id`, `city_id`, `district_id`, `location`, `latitude`, `longitude`, `number_bedroom`, `number_bathroom`, `number_floor`, `square`, `price`, `currency_id`, `bills_included`, `utilities`, `furnished`, `pets_allowed`, `smoking_allowed`, `online_view_tour`, `required_documents`, `content`, `video_thumbnail`, `author_id`, `author_type`, `expire_date`, `updated_at`, `created_at`) values ('', '', 'min_1_month', '', '[\\\"accounts\\\\/m-233\\\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\\\"]', 'test', '22', null, '8175', null, 'Testaccio, Rome, Metropolitan City of Rome Capital, Italy', '41.875952', '12.475694', '2', '3', '2', '3212', '3212', '1', '0', null, '1', '0', '0', '0', '[\\\"bank_statement\\\"]', '', null, 15, 'Xmetr\\\\RealEstate\\\\Models\\\\Account', '2025-08-24 21:39:11', '2025-07-25 21:39:11', '2025-07-25 21:39:11')", "type": "query", "params": [], "bindings": [{"value": null, "label": ""}, {"value": null, "label": ""}, {"value": "min_1_month", "label": "Min. 1 month"}, {"value": null, "label": ""}, "[\"accounts\\/m-233\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\"]", "test", "22", null, "8175", null, "Testaccio, Rome, Metropolitan City of Rome Capital, Italy", "41.875952", "12.475694", "2", "3", "2", "3212", "3212", "1", "0", null, "1", "0", "0", "0", "[\"bank_statement\"]", "", null, 15, "Xmetr\\RealEstate\\Models\\Account", "2025-08-24 21:39:11", "2025-07-25 21:39:11", "2025-07-25 21:39:11"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 126}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9096699, "duration": 0.00816, "duration_str": "8.16ms", "memory": 0, "memory_str": null, "filename": "AccountPropertyController.php:126", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 126}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:126", "ajax": false, "filename": "AccountPropertyController.php", "line": "126"}, "connection": "xmetr", "explain": null, "start_percent": 1.202, "width_percent": 3.83}, {"sql": "select * from `re_property_features` where `re_property_features`.`property_id` = 1180", "type": "query", "params": [], "bindings": [1180], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 132}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.925285, "duration": 0.008919999999999999, "duration_str": "8.92ms", "memory": 0, "memory_str": null, "filename": "AccountPropertyController.php:132", "source": {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 132}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:132", "ajax": false, "filename": "AccountPropertyController.php", "line": "132"}, "connection": "xmetr", "explain": null, "start_percent": 5.032, "width_percent": 4.187}, {"sql": "insert into `re_property_features` (`feature_id`, `property_id`) values (1, 1180)", "type": "query", "params": [], "bindings": [1, 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 132}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.936243, "duration": 0.037950000000000005, "duration_str": "37.95ms", "memory": 0, "memory_str": null, "filename": "AccountPropertyController.php:132", "source": {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 132}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:132", "ajax": false, "filename": "AccountPropertyController.php", "line": "132"}, "connection": "xmetr", "explain": null, "start_percent": 9.219, "width_percent": 17.814}, {"sql": "update `re_properties` set `suitable_for` = '[]', `re_properties`.`updated_at` = '2025-07-25 21:39:11' where `id` = 1180", "type": "query", "params": [], "bindings": ["[]", "2025-07-25 21:39:11", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 136}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9786048, "duration": 0.0051600000000000005, "duration_str": "5.16ms", "memory": 0, "memory_str": null, "filename": "AccountPropertyController.php:136", "source": {"index": 14, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 136}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:136", "ajax": false, "filename": "AccountPropertyController.php", "line": "136"}, "connection": "xmetr", "explain": null, "start_percent": 27.032, "width_percent": 2.422}, {"sql": "delete from `re_facilities_distances` where `re_facilities_distances`.`reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property'", "type": "query", "params": [], "bindings": [1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 138}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9871461, "duration": 0.011550000000000001, "duration_str": "11.55ms", "memory": 0, "memory_str": null, "filename": "SaveFacilitiesService.php:12", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/SaveFacilitiesService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\SaveFacilitiesService.php", "line": 12}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FSaveFacilitiesService.php:12", "ajax": false, "filename": "SaveFacilitiesService.php", "line": "12"}, "connection": "xmetr", "explain": null, "start_percent": 29.455, "width_percent": 5.422}, {"sql": "delete from `re_property_categories` where `re_property_categories`.`property_id` = 1180", "type": "query", "params": [], "bindings": [1180], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 18}, {"index": 13, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 140}, {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": ********52.0005898, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StorePropertyCategoryService.php:18", "source": {"index": 12, "namespace": null, "name": "platform/plugins/real-estate/src/Services/StorePropertyCategoryService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Services\\StorePropertyCategoryService.php", "line": 18}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FServices%2FStorePropertyCategoryService.php:18", "ajax": false, "filename": "StorePropertyCategoryService.php", "line": "18"}, "connection": "xmetr", "explain": null, "start_percent": 34.876, "width_percent": 0.512}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\CreatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": ********52.00716, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 35.388, "width_percent": 0.728}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('seo_meta', 1180, 'Xmetr\\\\RealEstate\\\\Models\\\\Property', '[{\\\"index\\\":\\\"index\\\"}]', '2025-07-25 21:39:12', '2025-07-25 21:39:12')", "type": "query", "params": [], "bindings": ["seo_meta", 1180, "Xmetr\\RealEstate\\Models\\Property", "[{\"index\":\"index\"}]", "2025-07-25 21:39:12", "2025-07-25 21:39:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 19, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\CreatedContentListener.php", "line": 15}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}], "start": ********52.011917, "duration": 0.01036, "duration_str": "10.36ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "xmetr", "explain": null, "start_percent": 36.115, "width_percent": 4.863}, {"sql": "select `lang_code`, `lang_is_default` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}], "start": ********52.029164, "duration": 0.01238, "duration_str": "12.38ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 40.978, "width_percent": 5.811}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'ru_RU' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["ru_RU", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.044037, "duration": 0.013349999999999999, "duration_str": "13.35ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 46.789, "width_percent": 6.266}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 're_properties_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.0586278, "duration": 0.01376, "duration_str": "13.76ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 53.056, "width_percent": 6.459}, {"sql": "insert into `re_properties_translations` (`name`, `description`, `original_description`, `content`, `location`, `lang_code`, `re_properties_id`) values (null, null, 'test', '', 'Testaccio, Rome, Metropolitan City of Rome Capital, Italy', 'ru_RU', 1180)", "type": "query", "params": [], "bindings": [null, null, "test", "", "Testaccio, Rome, Metropolitan City of Rome Capital, Italy", "ru_RU", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.0752969, "duration": 0.0072699999999999996, "duration_str": "7.27ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 59.515, "width_percent": 3.413}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'es_CL' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["es_CL", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.084857, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 62.927, "width_percent": 0.404}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 're_properties_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.086889, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 63.331, "width_percent": 1.887}, {"sql": "insert into `re_properties_translations` (`name`, `description`, `original_description`, `content`, `location`, `lang_code`, `re_properties_id`) values (null, null, 'test', '', 'Testaccio, Rome, Metropolitan City of Rome Capital, Italy', 'es_CL', 1180)", "type": "query", "params": [], "bindings": [null, null, "test", "", "Testaccio, Rome, Metropolitan City of Rome Capital, Italy", "es_CL", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.0941072, "duration": 0.0087, "duration_str": "8.7ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 65.218, "width_percent": 4.084}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'fr_FR' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["fr_FR", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.1047478, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 69.302, "width_percent": 0.535}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 're_properties_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.1070108, "duration": 0.00635, "duration_str": "6.35ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 69.837, "width_percent": 2.981}, {"sql": "insert into `re_properties_translations` (`name`, `description`, `original_description`, `content`, `location`, `lang_code`, `re_properties_id`) values (null, null, 'test', '', 'Testaccio, Rome, Metropolitan City of Rome Capital, Italy', 'fr_FR', 1180)", "type": "query", "params": [], "bindings": [null, null, "test", "", "Testaccio, Rome, Metropolitan City of Rome Capital, Italy", "fr_FR", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.116259, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 72.817, "width_percent": 1.812}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'de_DE' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["de_DE", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.1220338, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 74.629, "width_percent": 0.76}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 're_properties_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 22, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.124862, "duration": 0.00634, "duration_str": "6.34ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 75.39, "width_percent": 2.976}, {"sql": "insert into `re_properties_translations` (`name`, `description`, `original_description`, `content`, `location`, `lang_code`, `re_properties_id`) values (null, null, 'test', '', 'Testaccio, Rome, Metropolitan City of Rome Capital, Italy', 'de_DE', 1180)", "type": "query", "params": [], "bindings": [null, null, "test", "", "Testaccio, Rome, Metropolitan City of Rome Capital, Italy", "de_DE", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.134396, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 78.366, "width_percent": 1.807}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/audit-log/src/Events/AuditHandlerEvent.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Events\\AuditHandlerEvent.php", "line": 23}], "start": ********52.143426, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 80.173, "width_percent": 0.183}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'created', 1, 1, 1180, 'ID: 1180', 'info', '2025-07-25 21:39:12', '2025-07-25 21:39:12', '{\\\"images\\\":[\\\"accounts\\\\/m-233\\\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\\\"],\\\"original_description\\\":\\\"test\\\",\\\"country_id\\\":\\\"22\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8175\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Testaccio, Rome, Metropolitan City of Rome Capital, Italy\\\",\\\"latitude\\\":\\\"41.875952\\\",\\\"longitude\\\":\\\"12.475694\\\",\\\"number_bedroom\\\":\\\"2\\\",\\\"number_bathroom\\\":\\\"3\\\",\\\"number_floor\\\":\\\"2\\\",\\\"square\\\":\\\"3212\\\",\\\"price\\\":\\\"3212\\\",\\\"currency_id\\\":\\\"1\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"rental_period\\\":\\\"min_1_month\\\",\\\"required_documents\\\":[\\\"bank_statement\\\"],\\\"features\\\":[\\\"1\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"save\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "created", 1, 1, 1180, "ID: 1180", "info", "2025-07-25 21:39:12", "2025-07-25 21:39:12", "{\"images\":[\"accounts\\/m-233\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\"],\"original_description\":\"test\",\"country_id\":\"22\",\"state_id\":null,\"city_id\":\"8175\",\"district_id\":null,\"location\":\"Testaccio, Rome, Metropolitan City of Rome Capital, Italy\",\"latitude\":\"41.875952\",\"longitude\":\"12.475694\",\"number_bedroom\":\"2\",\"number_bathroom\":\"3\",\"number_floor\":\"2\",\"square\":\"3212\",\"price\":\"3212\",\"currency_id\":\"1\",\"commission\":null,\"deposit\":null,\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"rental_period\":\"min_1_month\",\"required_documents\":[\"bank_statement\"],\"features\":[\"1\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"save\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 142}], "start": ********52.1490529, "duration": 0.00775, "duration_str": "7.75ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 80.356, "width_percent": 3.638}, {"sql": "insert into `re_account_activity_logs` (`action`, `reference_name`, `reference_url`, `user_agent`, `ip_address`, `account_id`, `updated_at`, `created_at`) values ('create_property', null, '/en/account/properties/edit/1180', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 15, '2025-07-25 21:39:12', '2025-07-25 21:39:12')", "type": "query", "params": [], "bindings": ["create_property", null, "/en/account/properties/edit/1180", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", 15, "2025-07-25 21:39:12", "2025-07-25 21:39:12"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 146}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": ********52.177187, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "AccountPropertyController.php:146", "source": {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 146}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:146", "ajax": false, "filename": "AccountPropertyController.php", "line": "146"}, "connection": "xmetr", "explain": null, "start_percent": 83.994, "width_percent": 8.848}, {"sql": "select * from `re_accounts` where `re_accounts`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 162}, {"index": 26, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": ********52.200943, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 92.842, "width_percent": 1.253}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'commission' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["commission", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 898}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": ********52.207394, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 94.095, "width_percent": 0.263}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'deposit' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property')", "type": "query", "params": [], "bindings": ["deposit", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 65}, {"index": 15, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 899}, {"index": 19, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}], "start": ********52.2103002, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php:201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "xmetr", "explain": null, "start_percent": 94.358, "width_percent": 0.207}, {"sql": "update `re_properties` set `type` = 'rent', `period` = 'month', `re_properties`.`updated_at` = '2025-07-25 21:39:12' where `id` = 1180", "type": "query", "params": [], "bindings": [{"value": "rent", "label": "Rent"}, {"value": "month", "label": "Monthly"}, "2025-07-25 21:39:12", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 902}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 500}, {"index": 25, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": ********52.2395399, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "functions.php:902", "source": {"index": 18, "namespace": null, "name": "platform/themes/xmetr/functions/functions.php", "file": "D:\\laragon\\www\\xmetr\\platform\\themes\\xmetr\\functions\\functions.php", "line": 902}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fthemes%2Fxmetr%2Ffunctions%2Ffunctions.php:902", "ajax": false, "filename": "functions.php", "line": "902"}, "connection": "xmetr", "explain": null, "start_percent": 94.564, "width_percent": 1.91}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1180 and `reference_type` = 'Xmetr\\\\RealEstate\\\\Models\\\\Property') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1180, "Xmetr\\RealEstate\\Models\\Property"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\CreatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": ********52.270369, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 96.475, "width_percent": 0.305}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'ru_RU' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["ru_RU", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.274124, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 96.78, "width_percent": 0.493}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'es_CL' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["es_CL", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.277044, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 97.273, "width_percent": 0.239}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'fr_FR' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["fr_FR", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.279274, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 97.512, "width_percent": 0.188}, {"sql": "select exists(select * from `re_properties_translations` where (`lang_code` = 'de_DE' and `re_properties_id` = 1180)) as `exists`", "type": "query", "params": [], "bindings": ["de_DE", 1180], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\AccountPropertyController.php", "line": 103}], "start": ********52.281108, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php:30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 97.7, "width_percent": 0.202}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'property', 'created', 1, 1, 1180, 'ID: 1180', 'info', '2025-07-25 21:39:12', '2025-07-25 21:39:12', '{\\\"images\\\":[\\\"accounts\\\\/m-233\\\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\\\"],\\\"original_description\\\":\\\"test\\\",\\\"country_id\\\":\\\"22\\\",\\\"state_id\\\":null,\\\"city_id\\\":\\\"8175\\\",\\\"district_id\\\":null,\\\"location\\\":\\\"Testaccio, Rome, Metropolitan City of Rome Capital, Italy\\\",\\\"latitude\\\":\\\"41.875952\\\",\\\"longitude\\\":\\\"12.475694\\\",\\\"number_bedroom\\\":\\\"2\\\",\\\"number_bathroom\\\":\\\"3\\\",\\\"number_floor\\\":\\\"2\\\",\\\"square\\\":\\\"3212\\\",\\\"price\\\":\\\"3212\\\",\\\"currency_id\\\":\\\"1\\\",\\\"commission\\\":null,\\\"deposit\\\":null,\\\"bills_included\\\":\\\"0\\\",\\\"utilities\\\":null,\\\"furnished\\\":\\\"1\\\",\\\"pets_allowed\\\":\\\"0\\\",\\\"smoking_allowed\\\":\\\"0\\\",\\\"online_view_tour\\\":\\\"0\\\",\\\"rental_period\\\":\\\"min_1_month\\\",\\\"required_documents\\\":[\\\"bank_statement\\\"],\\\"features\\\":[\\\"1\\\"],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"submitter\\\":\\\"save\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "property", "created", 1, 1, 1180, "ID: 1180", "info", "2025-07-25 21:39:12", "2025-07-25 21:39:12", "{\"images\":[\"accounts\\/m-233\\/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp\"],\"original_description\":\"test\",\"country_id\":\"22\",\"state_id\":null,\"city_id\":\"8175\",\"district_id\":null,\"location\":\"Testaccio, Rome, Metropolitan City of Rome Capital, Italy\",\"latitude\":\"41.875952\",\"longitude\":\"12.475694\",\"number_bedroom\":\"2\",\"number_bathroom\":\"3\",\"number_floor\":\"2\",\"square\":\"3212\",\"price\":\"3212\",\"currency_id\":\"1\",\"commission\":null,\"deposit\":null,\"bills_included\":\"0\",\"utilities\":null,\"furnished\":\"1\",\"pets_allowed\":\"0\",\"smoking_allowed\":\"0\",\"online_view_tour\":\"0\",\"rental_period\":\"min_1_month\",\"required_documents\":[\"bank_statement\"],\"features\":[\"1\"],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"submitter\":\"save\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": ********52.2840118, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php:60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 97.902, "width_percent": 2.098}]}, "models": {"data": {"Xmetr\\RealEstate\\Models\\Currency": {"value": 43, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Facility": {"value": 23, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFacility.php:1", "ajax": false, "filename": "Facility.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Feature": {"value": 21, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FFeature.php:1", "ajax": false, "filename": "Feature.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Project": {"value": 8, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FProject.php:1", "ajax": false, "filename": "Project.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Category": {"value": 3, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FCategory.php:1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 2, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php:1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}}, "count": 107, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create.store", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store", "uri": "POST en/account/properties/create", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:91\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts", "prefix": "en/account/properties", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FAccountPropertyController.php:91\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/Fronts/AccountPropertyController.php:91-175</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, account, Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved", "duration": "1.37s", "peak_memory": "50MB", "response": "Redirect to https://xmetr.gc/en/listing-submitted", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"115 characters\">accounts/m-233/hd-wallpaper-gold-bars-gold-bullion-finance-concepts-gold-money-gold-coins-background-with-gold.webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>original_description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>state_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>city_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8175</span>\"\n  \"<span class=sf-dump-key>district_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"57 characters\">Testaccio, Rome, Metropolitan City of Rome Capital, Italy</span>\"\n  \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">41.875952</span>\"\n  \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12.475694</span>\"\n  \"<span class=sf-dump-key>number_bedroom</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>number_bathroom</span>\" => \"<span class=sf-dump-str>3</span>\"\n  \"<span class=sf-dump-key>number_floor</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>square</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3212</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>commission</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>deposit</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bills_included</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>utilities</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>furnished</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>pets_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>smoking_allowed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>online_view_tour</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>rental_period</span>\" => \"<span class=sf-dump-str title=\"11 characters\">min_1_month</span>\"\n  \"<span class=sf-dump-key>required_documents</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">bank_statement</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>features</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-964650538 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3852</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarynxFLDvjxRSusjMTA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6IjBkNDVlYTc2LTZmNjEtNGU3ZS04ZDJiLTdjYmM4OGE0Zjc5ZSIsImMiOjE3NTM0Nzg1MzY4MDMsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753478537$o105$g1$t1753478577$j20$l0$h0; XSRF-TOKEN=eyJpdiI6Ii95bU9SZWxDSkJTVDB3SzJLNkFBcXc9PSIsInZhbHVlIjoiTGxCSDRlaHpUQ0xVZDVFN2VIcW9MUkdldVZ4aHVvZDhmSzRYTjZENmIyMmpGK2ZteFBnZlRDM0U5RmE2bzArQ0VhMmU4aGFXa054U3RIQytySkxzbC9tYlpGTDNIOFdRZFg4MFpNa0ZtWTZob1UwSUZsMlRuOUNneWNRdEtGYXciLCJtYWMiOiIyYzNmZWQ0MzQ3MWQ2MjE1NDdlYjE4MDFmNmNlM2U5MDFmYjRmZWJjNzNmNjNmOTVkM2NkM2U5ODdmZjhmMWIxIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IjVnMnpaUnV6RDhXRzM5ZUxLYlJkVEE9PSIsInZhbHVlIjoiTStoTG1JR3Uwc0I4UCtyaEZhZ3BwNzh4QmpLU0w2R1h2dU1zRm51UVM5Z1hXdTNqRE4reklwR29Da1BWQUVYQjJ4TU1GTUhIQUpZMHpHc3FqMldGWnhmVDF5TURZWW91Szk2WmM3WjlrYnkrZ2tmaXVJSnE3UjByR0lvRURORDkiLCJtYWMiOiJhNzVhOTE2YzA0NzM0MzM2YzFlNjEyZTRiODBlNDExMzA3YWU1ODM4MWY2MTUyZGVkY2FkZGVkZTU1NjM1YjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-964650538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-627528327 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sr8ZvB9DucEYA9CROr9RC5CONMte5XinBBSwaJ1i</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627528327\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1588661413 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 21:39:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://xmetr.gc/en/listing-submitted</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588661413\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/en/account/properties/create", "action_name": "public.account.properties.create.store", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\AccountPropertyController@store"}, "badge": "302 Found"}}