<?php $__env->startSection('content'); ?>
    <?php echo apply_filters(ACCOUNT_TOP_STATISTIC_FILTER, null); ?>


    <div class="mb-3 row row-cards">
        <div class="col-12 col-md-6 col-lg-4 dashboard-widget-item">
            <a class="overflow-hidden text-white rounded d-block position-relative text-decoration-none bg-primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="px-4 py-3 details d-flex flex-column justify-content-between">
                        <div class="desc fw-medium"><?php echo e(trans('plugins/real-estate::dashboard.approved_properties')); ?></div>
                        <div class="number fw-bolder">
                            <?php echo e($user->properties()->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::APPROVED)->count()); ?>

                        </div>
                    </div>
                    <div class="pb-5 visual ps-1 position-absolute end-0">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-circle-check'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'me-n2','style' => 'opacity: 0.1;']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-12 col-md-6 col-lg-4 dashboard-widget-item">
            <a class="overflow-hidden text-white rounded d-block position-relative text-decoration-none bg-danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="px-4 py-3 details d-flex flex-column justify-content-between">
                        <div class="desc fw-medium"><?php echo e(trans('plugins/real-estate::dashboard.pending_approve_properties')); ?></div>
                        <div class="number fw-bolder">
                            <?php echo e($user->properties()->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::PENDING)->count()); ?>

                        </div>
                    </div>
                    <div class="pb-5 visual ps-1 position-absolute end-0">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-clock-hour-8'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'me-n2','style' => 'opacity: 0.1;']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-12 col-md-6 col-lg-4 dashboard-widget-item">
            <a class="overflow-hidden text-white rounded d-block position-relative text-decoration-none bg-secondary">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="px-4 py-3 details d-flex flex-column justify-content-between">
                        <div class="desc fw-medium"><?php echo e(trans('plugins/real-estate::dashboard.rejected_properties')); ?></div>
                        <div class="number fw-bolder">
                            <?php echo e($user->properties()->where('moderation_status', \Xmetr\RealEstate\Enums\ModerationStatusEnum::REJECTED)->count()); ?>

                        </div>
                    </div>
                    <div class="pb-5 visual ps-1 position-absolute end-0">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-edit'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'me-n2','style' => 'opacity: 0.1;']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <activity-log-component ajax-url="<?php echo e(route('public.account.activity-logs')); ?>" v-slot="{ activityLogs, loading }">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    <?php echo e(trans('plugins/real-estate::dashboard.activity_logs')); ?>

                </h4>
            </div>
            <div class="card-body" style="min-height: 15rem" v-if="loading">
                <div class="loading-spinner"></div>
            </div>

            <template v-else>
                <div class="empty" v-if="(!activityLogs?.meta || activityLogs?.meta?.total === 0)">
                    <div class="empty-icon">
                        <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-ghost'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                    </div>
                    <p class="empty-title">
                        <?php echo e(trans('plugins/real-estate::dashboard.no_activity_logs_title')); ?>

                    </p>
                    <p class="empty-subtitle text-muted">
                        <?php echo e(trans('plugins/real-estate::dashboard.no_activity_logs')); ?>

                    </p>
                </div>

                <div v-if="activityLogs?.meta?.total !== 0" class="list-group list-group-flush">
                    <div v-for="activityLog in activityLogs.data" :key="activityLog.id" class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <?php if (isset($component)) { $__componentOriginalaefae78ad685b1f1f5d54e36140c551e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e = $attributes; } ?>
<?php $component = Xmetr\Icon\View\Components\Icon::resolve(['name' => 'ti ti-clock'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Xmetr\Icon\View\Components\Icon::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $attributes = $__attributesOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__attributesOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e)): ?>
<?php $component = $__componentOriginalaefae78ad685b1f1f5d54e36140c551e; ?>
<?php unset($__componentOriginalaefae78ad685b1f1f5d54e36140c551e); ?>
<?php endif; ?>
                            </div>
                            <div class="col text-truncate">
                                <div class="text-reset d-block">
                                    <span :title="$sanitize(activityLog.description, { allowedTags: [] })" v-html="$sanitize(activityLog.description)"></span>
                                    <a :href="'https://whatismyipaddress.com/ip/' + activityLog.ip_address" target="_blank" :title="activityLog.ip_address">
                                        ({{ activityLog.ip_address }})
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="activityLogs?.links?.next" class="card-footer">
                    <a href="javascript:void(0)" v-if="!loading" @click="getActivityLogs(activityLogs.links.next)">
                        <?php echo e(trans('plugins/real-estate::dashboard.load_more')); ?>

                    </a>

                    <a href="javascript:void(0)" v-if="loading"><?php echo e(trans('plugins/real-estate::dashboard.loading_more')); ?></a>
                </div>
            </template>
        </div>
    </activity-log-component>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('plugins/real-estate::themes.dashboard.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/themes/dashboard/index.blade.php ENDPATH**/ ?>