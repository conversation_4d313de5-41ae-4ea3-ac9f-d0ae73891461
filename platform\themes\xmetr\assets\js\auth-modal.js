/**
 * Authentication Modal Handler
 * Handles login and registration form submissions in modal dialogs
 * with redirect-after-login functionality
 */

$(document).ready(function() {

    /**
     * Handle login form submission in modal (Xmetr theme)
     */
    $(document).on('submit', '#modalSignin form, #xmetr-login-form', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $modal = $('#modalSignin');

        // Show loading state
        $button.prop('disabled', true).addClass('btn-loading');

        $.ajax({
            type: 'POST',
            url: $form.attr('action'),
            data: new FormData($form[0]),
            contentType: false,
            processData: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.error === false) {
                    // Hide modal
                    $modal.modal('hide');

                    // Show success message
                    if (typeof Theme !== 'undefined' && Theme.showSuccess) {
                        Theme.showSuccess(response.message || 'Login successful');
                    }

                    // Redirect to intended URL or default
                    const redirectUrl = response.data && response.data.redirect_url
                        ? response.data.redirect_url
                        : window.location.href;

                    // Small delay to allow modal to close and message to show
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 500);
                } else {
                    // Show error message
                    if (typeof Theme !== 'undefined' && Theme.showError) {
                        Theme.showError(response.message || 'Login failed');
                    }
                }
            },
            error: function(xhr) {
                // Handle validation errors and other errors
                if (typeof Theme !== 'undefined' && Theme.handleError) {
                    Theme.handleError(xhr);
                } else {
                    let errorMessage = 'An error occurred during login';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        // Handle validation errors
                        const errors = xhr.responseJSON.errors;
                        const errorMessages = [];

                        for (const field in errors) {
                            if (errors.hasOwnProperty(field)) {
                                errorMessages.push(...errors[field]);
                            }
                        }

                        errorMessage = errorMessages.join('<br>');
                    }

                    // Show error message (fallback)
                    alert(errorMessage);
                }
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).removeClass('btn-loading');

                // Refresh recaptcha if available
                if (typeof refreshRecaptcha !== 'undefined') {
                    refreshRecaptcha();
                }
            }
        });
    });

    /**
     * Handle registration form submission in modal (Xmetr theme)
     */
    $(document).on('submit', '#modalSignup form, #xmetr-register-form', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $button = $form.find('button[type="submit"]');
        const $modal = $('#modalSignup');

        // Show loading state
        $button.prop('disabled', true).addClass('btn-loading');

        $.ajax({
            type: 'POST',
            url: $form.attr('action'),
            data: new FormData($form[0]),
            contentType: false,
            processData: false,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.error === false) {
                    // Hide modal
                    $modal.modal('hide');

                    // Show success message
                    if (typeof Theme !== 'undefined' && Theme.showSuccess) {
                        Theme.showSuccess(response.message || 'Registration successful');
                    }

                    // Redirect to intended URL or default
                    const redirectUrl = response.data && response.data.redirect_url
                        ? response.data.redirect_url
                        : window.location.href;

                    // Small delay to allow modal to close and message to show
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 500);
                } else {
                    // Show error message
                    if (typeof Theme !== 'undefined' && Theme.showError) {
                        Theme.showError(response.message || 'Registration failed');
                    }
                }
            },
            error: function(xhr) {
                // Handle validation errors and other errors
                if (typeof Theme !== 'undefined' && Theme.handleError) {
                    Theme.handleError(xhr);
                } else {
                    let errorMessage = 'An error occurred during registration';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        // Handle validation errors
                        const errors = xhr.responseJSON.errors;
                        const errorMessages = [];

                        for (const field in errors) {
                            if (errors.hasOwnProperty(field)) {
                                errorMessages.push(...errors[field]);
                            }
                        }

                        errorMessage = errorMessages.join('<br>');
                    }

                    // Show error message (fallback)
                    alert(errorMessage);
                }
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false).removeClass('btn-loading');

                // Refresh recaptcha if available
                if (typeof refreshRecaptcha !== 'undefined') {
                    refreshRecaptcha();
                }
            }
        });
    });

    /**
     * Store current URL when login/register modal is opened
     * This ensures we capture the intended URL even if the user
     * opens the modal manually (not through middleware redirect)
     */
    $('#modalSignin, #modalSignup').on('show.bs.modal', function() {
        // Update the intended URL in the hidden fields
        const currentUrl = window.location.href;
        $('#login-intended-url').val(currentUrl);
        $('#register-intended-url').val(currentUrl);

        // Also store in session via AJAX as backup
        $.ajax({
            type: 'POST',
            url: window.location.origin + '/store-intended-url',
            data: {
                intended_url: currentUrl,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            // Silent request - don't handle errors
            error: function() {
                // Ignore errors for this helper request
            }
        });
    });
});
