{"__meta": {"id": "01K11RBWYZAMAR6BR606MWP3WT", "datetime": "2025-07-25 21:38:28", "utime": **********.960256, "method": "POST", "uri": "/en/account/ajax/upload", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.179533, "end": **********.960281, "duration": 13.***************, "duration_str": "13.78s", "measures": [{"label": "Booting", "start": **********.179533, "relative_start": 0, "end": **********.097489, "relative_end": **********.097489, "duration": 0.****************, "duration_str": "918ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.097501, "relative_start": 0.****************, "end": **********.960284, "relative_end": 3.0994415283203125e-06, "duration": 12.***************, "duration_str": "12.86s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.17862, "relative_start": 0.****************, "end": **********.197157, "relative_end": **********.197157, "duration": 0.018536806106567383, "duration_str": "18.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.904571, "relative_start": 13.***************, "end": **********.957461, "relative_end": **********.957461, "duration": 0.*****************, "duration_str": "52.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::9a1da6c7f662474948fe63691d3a1543", "start": **********.91598, "relative_start": 13.***************, "end": **********.91598, "relative_end": **********.91598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "68MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": **********.915947, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php:1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}]}, "queries": {"count": 481, "nb_statements": 481, "nb_visible_statements": 481, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.****************, "accumulated_duration_str": "389ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 381 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `re_accounts` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "account", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Middleware\\RedirectIfNotAccount.php", "line": 16}], "start": **********.207951, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.234}, {"sql": "select * from `media_folders` where (`slug` = 'accounts' and `parent_id` = 0) and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["accounts", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 958}, {"index": 18, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3235528, "duration": 0.02042, "duration_str": "20.42ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0.234, "width_percent": 5.244}, {"sql": "select * from `media_folders` where (`slug` = 'М.' and `parent_id` = 10) and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["М.", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 958}, {"index": 18, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 20, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.346003, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 5.478, "width_percent": 1.274}, {"sql": "select * from `media_folders` where (`slug` = 'М.' and `parent_id` = 10) and `media_folders`.`deleted_at` is null order by `media_folders`.`id` asc limit 1000 offset 0", "type": "query", "params": [], "bindings": ["М.", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 967}, {"index": 19, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 21, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.353914, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 6.752, "width_percent": 0.439}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 971}], "start": **********.3587868, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php:48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.191, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3611522, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 7.299, "width_percent": 0.259}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-1' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-1", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.364251, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 7.558, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-2' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-2", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3657808, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 7.697, "width_percent": 0.244}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-3' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-3", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.36868, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 7.941, "width_percent": 0.262}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-4' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-4", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.371131, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 8.203, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-5' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-5", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.372742, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 8.359, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-6' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-6", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.374165, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 8.493, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-7' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-7", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.376338, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 8.709, "width_percent": 0.419}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-8' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-8", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.379384, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 9.127, "width_percent": 0.185}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-9' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-9", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.382008, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 9.312, "width_percent": 0.185}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-10' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-10", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.384633, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 9.497, "width_percent": 0.185}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-11' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-11", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.386479, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 9.682, "width_percent": 0.272}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-12' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-12", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.388652, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 9.954, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-13' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-13", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.390069, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.088, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-14' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-14", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.391529, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.234, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-15' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-15", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.392929, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.357, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-16' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-16", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.394604, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.527, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-17' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-17", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3962, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.694, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-18' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-18", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.397679, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 10.83, "width_percent": 0.388}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-19' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-19", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4002879, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 11.218, "width_percent": 0.241}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-20' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-20", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.402246, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 11.459, "width_percent": 0.347}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-21' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-21", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.404673, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 11.806, "width_percent": 0.218}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-22' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-22", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4065452, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.024, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-23' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-23", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.408014, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.15, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-24' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-24", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.40983, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.366, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-25' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-25", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4117231, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.497, "width_percent": 0.221}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-26' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-26", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.414473, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.718, "width_percent": 0.18}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-27' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-27", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.416138, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 12.897, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-28' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-28", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.417629, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.031, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-29' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-29", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.419472, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.172, "width_percent": 0.221}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-30' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-30", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4214709, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.393, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-31' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-31", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4231079, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.55, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-32' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-32", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4249709, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.696, "width_percent": 0.236}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-33' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-33", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.427029, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 13.932, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-34' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-34", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.42866, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 14.084, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-35' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-35", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.430197, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 14.223, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-36' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-36", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.431835, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 14.364, "width_percent": 0.182}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-37' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-37", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4343, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 14.546, "width_percent": 0.29}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-38' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-38", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.436645, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 14.836, "width_percent": 0.27}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-39' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-39", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.438867, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.106, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-40' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-40", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.44076, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.278, "width_percent": 0.18}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-41' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-41", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.442564, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.458, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-42' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-42", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.444691, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.599, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-43' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-43", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.446299, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.746, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-44' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-44", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.447844, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 15.879, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-45' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-45", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4493718, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 16.005, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-46' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-46", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4510682, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 16.156, "width_percent": 0.262}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-47' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-47", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.453819, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 16.418, "width_percent": 0.277}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-48' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-48", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4568021, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 16.696, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-49' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-49", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.458724, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 16.865, "width_percent": 0.159}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-50' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-50", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.46073, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.025, "width_percent": 0.229}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-51' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-51", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.463097, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.253, "width_percent": 0.159}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-52' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-52", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.464688, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.412, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-53' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-53", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.46627, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.541, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-54' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-54", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.46778, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.669, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-55' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-55", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.469737, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 17.787, "width_percent": 0.231}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-56' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-56", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.471632, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.018, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-57' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-57", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4730961, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.139, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-58' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-58", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.475048, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.268, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-59' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-59", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.477457, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.409, "width_percent": 0.303}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-60' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-60", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.479995, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.712, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-61' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-61", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.481616, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 18.85, "width_percent": 0.185}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-62' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-62", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.483462, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 19.035, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-63' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-63", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4852529, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 19.205, "width_percent": 0.334}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-64' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-64", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.487583, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 19.539, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-65' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-65", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.489284, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 19.68, "width_percent": 0.267}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-66' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-66", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.491561, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 19.947, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-67' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-67", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.493783, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 20.099, "width_percent": 0.203}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-68' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-68", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4964502, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 20.302, "width_percent": 0.249}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-69' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-69", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4985778, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 20.551, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-70' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-70", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.500401, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 20.702, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-71' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-71", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.501876, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 20.836, "width_percent": 0.303}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-72' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-72", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.504012, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 21.139, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-73' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-73", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.506572, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 21.354, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-74' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-74", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.508807, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 21.496, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-75' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-75", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5111048, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 21.64, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-76' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-76", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.513402, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 21.788, "width_percent": 0.239}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-77' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-77", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.516119, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.027, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-78' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-78", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.517764, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.192, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-79' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-79", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.519237, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.325, "width_percent": 0.229}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-80' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-80", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.521122, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.554, "width_percent": 0.247}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-81' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-81", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.523297, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.8, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-82' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-82", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5259912, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 22.975, "width_percent": 0.162}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-83' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-83", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.527756, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 23.137, "width_percent": 0.244}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-84' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-84", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.530412, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 23.381, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-85' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-85", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.532663, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 23.537, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-86' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-86", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.534825, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 23.709, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-87' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-87", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5370212, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 23.876, "width_percent": 0.303}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-88' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-88", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.540027, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 24.179, "width_percent": 0.229}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-89' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-89", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.542201, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 24.408, "width_percent": 0.218}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-90' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-90", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5450962, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 24.626, "width_percent": 0.252}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-91' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-91", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.548254, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 24.878, "width_percent": 0.275}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-92' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-92", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5509229, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 25.153, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-93' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-93", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.552922, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 25.291, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = 'М.-94' and `parent_id` = 10) as `exists`", "type": "query", "params": [], "bindings": ["М.-94", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, {"index": 12, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 972}, {"index": 13, "namespace": null, "name": "platform/core/media/src/RvMedia.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\RvMedia.php", "line": 493}, {"index": 15, "namespace": null, "name": "platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Http\\Controllers\\Fronts\\PublicAccountController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5549202, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MediaFolder.php:124", "source": {"index": 11, "namespace": null, "name": "platform/core/media/src/Models/MediaFolder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\media\\src\\Models\\MediaFolder.php", "line": 124}, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:124", "ajax": false, "filename": "MediaFolder.php", "line": "124"}, "connection": "xmetr", "explain": null, "start_percent": 25.42, "width_percent": 0.205}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.556704, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.625, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.557434, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.761, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5580919, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.882, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.558794, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.021, "width_percent": 0.262}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.559999, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.283, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.560735, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.416, "width_percent": 0.29}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.562169, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.707, "width_percent": 0.388}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.563976, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.094, "width_percent": 0.326}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.565535, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.421, "width_percent": 0.318}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5670142, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.739, "width_percent": 0.252}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5682092, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.991, "width_percent": 0.39}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.569955, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.381, "width_percent": 0.213}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.571035, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.594, "width_percent": 0.326}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.572661, "duration": 0.019309999999999997, "duration_str": "19.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.92, "width_percent": 4.959}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.592435, "duration": 0.006679999999999999, "duration_str": "6.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.88, "width_percent": 1.716}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.599449, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.595, "width_percent": 0.2}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.600426, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.795, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6012611, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.939, "width_percent": 0.622}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.604007, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.561, "width_percent": 0.236}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.605176, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.797, "width_percent": 0.177}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.606276, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.974, "width_percent": 0.886}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6102371, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.86, "width_percent": 0.267}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.611689, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.127, "width_percent": 0.303}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.613127, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.43, "width_percent": 0.316}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6146119, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.746, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.615384, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.895, "width_percent": 0.116}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.616023, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.011, "width_percent": 0.3}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6173868, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.311, "width_percent": 0.331}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.618866, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.643, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.619561, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.779, "width_percent": 0.288}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.620837, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.066, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.621401, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.174, "width_percent": 0.272}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.622604, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.446, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6231592, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.554, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.623774, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.677, "width_percent": 0.27}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6249688, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.947, "width_percent": 0.203}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6260378, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.15, "width_percent": 0.383}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627866, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.533, "width_percent": 0.306}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.629273, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.838, "width_percent": 0.231}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.630388, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.069, "width_percent": 0.283}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.631714, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.352, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.632622, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.521, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6334019, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.67, "width_percent": 0.367}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.635033, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.038, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6358368, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.189, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.636695, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.338, "width_percent": 0.203}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.637697, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.541, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6384661, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.69, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6391308, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.811, "width_percent": 0.336}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.640671, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.147, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6415389, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.311, "width_percent": 0.2}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.64259, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.512, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.643429, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.681, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644088, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.807, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.644943, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.979, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.645795, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.146, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6464841, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.28, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.647114, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.406, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.647767, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.536, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6484442, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.675, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6490471, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.793, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.649694, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.917, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650328, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.037, "width_percent": 0.218}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651336, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.256, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651979, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.384, "width_percent": 0.247}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653084, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.631, "width_percent": 0.18}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.653923, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.81, "width_percent": 0.46}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655855, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.27, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.656549, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.398, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65725, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.537, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658001, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.681, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6587539, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.835, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.659499, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.987, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660152, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.115, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660882, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.264, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.661493, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.387, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662125, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.518, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662719, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.636, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663321, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.754, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.663963, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.883, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.66462, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.014, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665265, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.142, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665906, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.271, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.666549, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.399, "width_percent": 0.226}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.667568, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.625, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.668239, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.761, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6692212, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.977, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670198, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.193, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.670857, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.326, "width_percent": 0.231}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671892, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.557, "width_percent": 0.223}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672897, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.781, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6735501, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.912, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6745489, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.127, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675256, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.271, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.675918, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.405, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676595, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.541, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6772652, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.674, "width_percent": 0.159}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678038, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.834, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.678721, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.972, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679331, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.096, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.679956, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.221, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.680593, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.347, "width_percent": 0.231}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681643, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.578, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.682376, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.73, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6830769, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.871, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683782, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.015, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.684456, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.151, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685124, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.287, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6858, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.423, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.686462, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.557, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687167, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.701, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687838, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.837, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688508, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.973, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.689193, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.112, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6899981, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.279, "width_percent": 0.252}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691132, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.53, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.691811, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.669, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.692492, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.808, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.69321, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.954, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.693898, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.093, "width_percent": 0.221}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.694897, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.314, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.695522, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.439, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696142, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.565, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.696751, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.689, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.697407, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.822, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.698124, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.968, "width_percent": 0.311}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.699502, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.279, "width_percent": 0.288}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7007911, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.567, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.701621, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.736, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7024322, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.903, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7032611, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.073, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.704075, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.24, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7049801, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.414, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.705831, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.589, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.706645, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.756, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7074761, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.925, "width_percent": 0.352}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7090151, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.277, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.709841, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.447, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `name` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.710674, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.619, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7331579, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.793, "width_percent": 0.19}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.734163, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.984, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.734854, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.104, "width_percent": 0.247}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7360868, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.351, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736711, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.459, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7373831, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.584, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738038, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.708, "width_percent": 0.303}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739405, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.011, "width_percent": 0.105}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.739991, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.116, "width_percent": 0.195}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7409122, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.311, "width_percent": 0.116}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741524, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.427, "width_percent": 0.187}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74243, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.614, "width_percent": 0.098}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7429812, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.712, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7435892, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.825, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744202, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.943, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7447832, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.053, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7453902, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.174, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7460089, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.297, "width_percent": 0.103}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.746541, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.4, "width_percent": 0.092}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74706, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.493, "width_percent": 0.162}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.747844, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.654, "width_percent": 0.095}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748364, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.749, "width_percent": 0.092}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.748868, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.842, "width_percent": 0.092}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.749372, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.934, "width_percent": 0.092}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7498748, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.027, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750432, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.135, "width_percent": 0.095}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7509382, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.23, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7515411, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.343, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7522001, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.469, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7528281, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.592, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7536342, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.764, "width_percent": 0.116}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.754224, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.879, "width_percent": 0.208}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.755168, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.087, "width_percent": 0.203}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7561, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.29, "width_percent": 0.103}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756641, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.393, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7572088, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.504, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.757762, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.611, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.758332, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.722, "width_percent": 0.103}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7588649, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.825, "width_percent": 0.193}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.759753, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.017, "width_percent": 0.098}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760284, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.115, "width_percent": 0.247}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7614129, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.361, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.472, "width_percent": 0.116}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762597, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.587, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7632039, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.7, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7640269, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.849, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764826, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.993, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.765533, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.129, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766184, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.252, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.766814, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.373, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.767438, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.491, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768166, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.63, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.768809, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.751, "width_percent": 0.205}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.769773, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.956, "width_percent": 0.131}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7704391, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.087, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7710688, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.208, "width_percent": 0.331}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7725291, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.539, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.773124, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.652, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.773735, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.77, "width_percent": 0.205}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7747068, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.976, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7752829, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.086, "width_percent": 0.198}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.776204, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.284, "width_percent": 0.116}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.776812, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.399, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.777423, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.518, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.77813, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.654, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7787411, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.772, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.77937, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.893, "width_percent": 0.211}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7804341, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.103, "width_percent": 0.121}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781046, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.224, "width_percent": 0.103}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.781589, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.327, "width_percent": 0.193}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.782492, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.519, "width_percent": 0.108}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.783067, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.627, "width_percent": 0.208}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7840471, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.835, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.784698, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.958, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785361, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.087, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7859938, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.213, "width_percent": 0.195}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7869072, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.408, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.787507, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.526, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7881079, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.644, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.78868, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.754, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7893152, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.883, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.789894, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.996, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.790471, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.109, "width_percent": 0.113}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791049, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.222, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791619, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.332, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.792216, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.45, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7928138, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.569, "width_percent": 0.11}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793387, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.679, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7940512, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.813, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.794694, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.936, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795464, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.087, "width_percent": 0.234}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7965481, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.321, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7973938, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.488, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.79816, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.645, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.799015, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.819, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.799736, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.958, "width_percent": 0.283}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801017, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.24, "width_percent": 0.118}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801635, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.359, "width_percent": 0.218}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.802639, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.577, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8032901, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.705, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.80392, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.831, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8045669, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.96, "width_percent": 0.203}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805511, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.162, "width_percent": 0.177}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.806343, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.34, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807036, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.481, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.807707, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.617, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.808327, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.74, "width_percent": 0.123}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.808947, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.864, "width_percent": 0.128}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.809603, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.992, "width_percent": 0.198}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.810541, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.19, "width_percent": 0.236}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.811643, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.426, "width_percent": 0.231}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.812702, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.657, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8133972, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.798, "width_percent": 0.216}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.814384, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.014, "width_percent": 0.126}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.815012, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.14, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.815698, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.274, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8164308, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.415, "width_percent": 0.244}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817561, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.659, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.818298, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.805, "width_percent": 0.226}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.819319, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.031, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81999, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.167, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.820702, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.314, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.821395, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.455, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.822091, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.599, "width_percent": 0.252}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823207, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.85, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823902, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.994, "width_percent": 0.241}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.824983, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.236, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8256578, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.374, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8263729, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.518, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.827129, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.67, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.827832, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.811, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.828533, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.95, "width_percent": 0.177}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.829396, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.127, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.830188, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.276, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.830892, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.412, "width_percent": 0.295}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8322492, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.707, "width_percent": 0.208}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833226, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.915, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833967, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.067, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8346798, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.213, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835379, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.357, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8360748, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.498, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.836817, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.645, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.837474, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.778, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838137, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.912, "width_percent": 0.134}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.838793, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.045, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839457, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.181, "width_percent": 0.236}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.840515, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.418, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84118, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.554, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841847, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.69, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.842517, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.826, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8432329, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.975, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8439689, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.129, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.844653, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.268, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.845327, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.406, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.846004, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.542, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.846762, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.697, "width_percent": 0.195}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8476849, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.892, "width_percent": 0.141}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.848389, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.033, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849103, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.177, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.849776, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.313, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.850453, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.449, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851165, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.595, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.851841, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.732, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.852517, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.868, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.853189, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.004, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.853857, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.14, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.854538, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.279, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.855208, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.415, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8559139, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.561, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.856593, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.7, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.857293, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.844, "width_percent": 0.136}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.857959, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.98, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.858668, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.126, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.859344, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.265, "width_percent": 0.139}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.860035, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.403, "width_percent": 0.221}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.86109, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.624, "width_percent": 0.2}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862105, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.825, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.862839, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.968, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863687, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.138, "width_percent": 0.193}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.864625, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.331, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8654542, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.503, "width_percent": 0.159}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.866232, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.662, "width_percent": 0.182}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.867095, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.844, "width_percent": 0.17}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8679051, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.014, "width_percent": 0.265}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.869078, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.278, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8698258, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.435, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.870562, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.586, "width_percent": 0.257}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.871716, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.843, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.872438, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.99, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8731759, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.139, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.873909, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.29, "width_percent": 0.254}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.875043, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.544, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.875757, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.691, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.876507, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.84, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8773808, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.012, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.878142, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.166, "width_percent": 0.262}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8793058, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.428, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8800569, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.584, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.880812, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.741, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.88153, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.89, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.882254, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.039, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.88297, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.188, "width_percent": 0.162}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.883737, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.35, "width_percent": 0.254}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.884863, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.604, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.885582, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.753, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.886302, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.902, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.887013, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.048, "width_percent": 0.146}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8877208, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.195, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888439, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.344, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889173, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.498, "width_percent": 0.149}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.889893, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.647, "width_percent": 0.159}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.890645, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.806, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.891374, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.957, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.892308, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.132, "width_percent": 0.175}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8931398, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.307, "width_percent": 0.144}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.893845, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.451, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8946428, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.617, "width_percent": 0.154}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8953788, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.772, "width_percent": 0.152}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.896124, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.923, "width_percent": 0.2}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8970559, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.123, "width_percent": 0.157}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.897794, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.28, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898569, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.444, "width_percent": 0.18}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.899409, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.624, "width_percent": 0.18}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.900245, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.804, "width_percent": 0.172}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.901057, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.976, "width_percent": 0.182}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.901901, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.158, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.902692, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.325, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903479, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.492, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.904258, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.657, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9050322, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.821, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.905816, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.988, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.906604, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.155, "width_percent": 0.164}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.907378, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.319, "width_percent": 0.167}, {"sql": "select exists(select * from `media_folders` where `slug` = ? and `parent_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.908236, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.486, "width_percent": 0.18}, {"sql": "insert into `media_folders` (`user_id`, `name`, `slug`, `parent_id`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9106371, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.666, "width_percent": 1.564}, {"sql": "select exists(select * from `media_files` where `name` = ? and `folder_id` = ?) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918513, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.23, "width_percent": 0.95}, {"sql": "select * from `media_folders` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922955, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.18, "width_percent": 0.128}, {"sql": "select * from `media_folders` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9242911, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.309, "width_percent": 0.116}, {"sql": "insert into `media_files` (`name`, `url`, `alt`, `size`, `mime_type`, `folder_id`, `user_id`, `options`, `visibility`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753479502.703438, "duration": 0.00841, "duration_str": "8.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.424, "width_percent": 2.16}, {"sql": "select * from `media_folders` where `media_folders`.`id` = ? and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753479502.735198, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.584, "width_percent": 0.172}, {"sql": "select * from `media_folders` where `media_folders`.`id` = ? and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753479502.736876, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.756, "width_percent": 0.144}, {"sql": "select * from `media_folders` where `media_folders`.`id` = ? and `media_folders`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1753479502.738441, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.9, "width_percent": 0.1}]}, "models": {"data": {"Xmetr\\Media\\Models\\MediaFolder": {"value": 5, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFolder.php:1", "ajax": false, "filename": "MediaFolder.php", "line": "?"}}, "Xmetr\\RealEstate\\Models\\Account": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FModels%2FAccount.php:1", "ajax": false, "filename": "Account.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/en/account/ajax/upload", "action_name": "public.account.upload", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PublicAccountController@postUpload", "uri": "POST en/account/ajax/upload", "controller": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PublicAccountController@postUpload<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicAccountController.php:433\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts", "prefix": "en/account/ajax", "file": "<a href=\"vscode://file/D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FHttp%2FControllers%2FFronts%2FPublicAccountController.php:433\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/real-estate/src/Http/Controllers/Fronts/PublicAccountController.php:433-497</a>", "middleware": "web, core, localeSessionRedirect, localizationRedirect, account, Xmetr\\RealEstate\\Http\\Middleware\\EnsureAccountIsApproved", "duration": "13.79s", "peak_memory": "70MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">112248</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryPee2qIYtAD9lEncm</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1709 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; wishlist=657; project_wishlist=25; _hjSession_6417422=eyJpZCI6IjBkNDVlYTc2LTZmNjEtNGU3ZS04ZDJiLTdjYmM4OGE0Zjc5ZSIsImMiOjE3NTM0Nzg1MzY4MDMsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1753478537$o105$g1$t1753478577$j20$l0$h0; XSRF-TOKEN=eyJpdiI6IjhXblpwc2NTcm85d1lrWFc2ZWpOMWc9PSIsInZhbHVlIjoiaEE0L2RQbUd0ZUVlejdBUjd1d2F6T1VURGhEY0o4WE9acXNqV0RWOGwvamZrdG5EKzlaOHFKakk1N2lKaHR2d0krb1cxMUcrNERUZXJpWnVsdE5XUzhsN0E4YVNyWVcxZzRyMnhlMTRMV2lBdGZmMU5RVVQyeTlscmk0RzJxRjgiLCJtYWMiOiI2Y2U4ZTQ4NzZhMmE4MzIyMjBkMDBmMzliNjEzNTQwNzQxZGM1MmY5MWUwZDM4ODMwYzI0ODljNGQwZGMzYThiIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InpwdTlUN1I1WFpOd2xKSm1IL3RWeUE9PSIsInZhbHVlIjoiekdhUlRLTm10cnlqTER3Qkd0emU3a2NIKzQ2VXAzWS9URW9OZS9xQ1ppSnF0am1QSmo5L0twR3B6NXpPN052QllwMzJrbGhJeEJSWTNaSFNJV3Z3ZURCYkFrWEdTYUlhU1U4Z2ROR0ZIL3QzaG5kR3E0WDJtdmNhSkJESXQyMy8iLCJtYWMiOiI2MDg1MzJhMTdlZWQ5YjdjNTdmZDliMDNkYTA0ZTIzYzc4MzgxMmJiOThmMmQ3OTBmYzgwYTNiYzE3OWRjMmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-969025428 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>project_wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Sr8ZvB9DucEYA9CROr9RC5CONMte5XinBBSwaJ1i</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969025428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1754592670 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 21:38:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754592670\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hNxJAMpdnDuAycsRH2HkZnoamTciDVeAOQBdfedW</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://xmetr.gc/en/account/properties/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>viewed_project</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_project_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>25</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>viewed_property_daily</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1172</span> => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_account_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/en/account/ajax/upload", "action_name": "public.account.upload", "controller_action": "Xmetr\\RealEstate\\Http\\Controllers\\Fronts\\PublicAccountController@postUpload"}, "badge": null}}