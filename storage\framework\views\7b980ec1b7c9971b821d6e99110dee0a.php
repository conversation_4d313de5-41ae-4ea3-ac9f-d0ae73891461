<?php
    $layout = 'plugins/real-estate::themes.dashboard.layouts.master';
?>



<?php $__env->startSection('content'); ?>

<style>
    .form-label {
        font-family: var(--title-font-family);
        font-weight: 700;
        font-size: 15px;
        color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
    }
    input[name="email"],
    label[for="email"] {
        display: none;
    }
    .select-location-fields div:nth-child(1){
        width: 100%;
    }
    .select-location-fields div:nth-child(2),
    .select-location-fields div:nth-child(3)
    {
        display: none
    }
    .preview-image-wrapper{
        width: 140px !important;
    }
    .image-box.image-box-avatar_image{
        display: flex;
        gap: 10px;
    }
    .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]{
        border-radius: 12px;
        display: inline-block;
        font-family: var(--title-font-family);
        font-weight: 600;
        font-size: 15px;
        font-style: normal;
        letter-spacing: 0em;
        padding: 13px 30px;
        position: relative;
        overflow: hidden;
        text-align: center;
        z-index: 0;
        width: fit-content;
        height: 50px;
        background-color: #ffffff;
        border: 1px solid var(--headings-color);
        -webkit-transition: all 0.4s ease;
        -moz-transition: all 0.4s ease;
        -ms-transition: all 0.4s ease;
        -o-transition: all 0.4s ease;
        transition: all 0.4s ease;
    }
    .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:before{
        background-color: #181A20;
        content: "";
        height: 100%;
        left: -100%;
        position: absolute;
        top: 0;
        width: 0;
        z-index: -1;
        -webkit-transform: skew(50deg);
        -moz-transform: skew(50deg);
        -o-transform: skew(50deg);
        transform: skew(50deg);
        -webkit-transition: width 0.6s;
        -moz-transition: width 0.6s;
        -o-transition: width 0.6s;
        transition: width 0.6s;
        transform-origin: top left;
    }
    .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:hover{
        border: 1px solid #181A20;
        color: #ffffff;
    }
    .image-box.image-box-avatar_image > a[data-bb-toggle="image-picker-choose"]:hover:before {
        background-color: #181A20;
        height: 100%;
        width: 200%;
    }
    .preview-image-wrapper .preview-image-inner .image-picker-remove-button {
        border-radius: 10px;
        background: #fbf0ee;
        left: 10px;
        right: unset;
        padding: 15px !important;
    }
    .btn-primary{
        background-color: #5e2dc2;
        border-color: #5e2dc2;
        color: #ffffff;
        border-radius: 10px;
        padding: 15px 24px;
        width: 100%;
    }
    .btn-primary:hover{
        background-color: #5026a5;
        border-color: #5026a5;
        color: #ffffff;
    }
    #xmetr-real-estate-forms-account-property-form .alert{
        display: none;
    }
    #xmetr-real-estate-forms-account-property-form .row .col-md-9{
        width: 100%;
    }
    #xmetr-real-estate-forms-account-property-form .card {
        border: none;
    }
    #xmetr-real-estate-forms-account-property-form .card .card-body {
        border: none;
        padding: 0;
    }
    .form-body .form-group.col-md-3{
        width: 50%;
    }
    .card-header {
        background: none;
        border: none;
        padding: 0;
    }
    .card-header h4{
        font-family: var(--title-font-family);
        font-weight: 700 !important;
        font-size: 15px !important;
        color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
    }

   #xmetr-real-estate-forms-account-property-form .box-amenities .col,
   #xmetr-real-estate-forms-account-property-form .box-suitable .col
   {
        width: 50% !important;
    }
    #xmetr-real-estate-forms-account-property-form .form-check {
        padding: 0;
        width: 100%;
    }
    #xmetr-real-estate-forms-account-property-form .form-check span{
        padding: 11px 16px;
        background: #F7F7F7;
        border-radius: 10px;
        transition: 0.1s ease-in-out;
        margin: 0;
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border: none;
        font-size: 15px;
        font-weight: 100;
        color: #000;
        height: 50px;
    }
    #xmetr-real-estate-forms-account-property-form .form-check input {
        display: none;
    }
    #xmetr-real-estate-forms-account-property-form  .form-check span:active {
        transform: scale(.95);
    }
    #xmetr-real-estate-forms-account-property-form .form-check input:checked + span {
        background: #5E2DC2;
        color: #fff;
        border-radius: 10px;
        padding: 11px 16px;
    }
/* ✅ Reset label span styling */
#xmetr-real-estate-forms-account-property-form .bills-included-form-group .form-check span {
    all: unset;
    display: inline;
    padding: 0;
    background: none;
    border-radius: 0;
    color: inherit;
    font-weight: normal;
    font-size: inherit;
    cursor: default;
}

/* ✅ Reset checkbox input styling */
#xmetr-real-estate-forms-account-property-form .bills-included-form-group .form-check input {
    display: inline-block !important;
    width: 1.2em;
    height: 1.2em;
    margin-right: 5px;
    cursor: pointer;
    margin-left: 0;
}

/* ✅ Optional: Reset checked state appearance */
#xmetr-real-estate-forms-account-property-form .bills-included-form-group .form-check input:checked + span {
    background: #5E2DC2;
    color: inherit;
    padding: 0;
    border-radius: 0;
}

    #xmetr-real-estate-forms-account-property-form #language_advanced_wrap{
        display: none;
    }

    #xmetr-real-estate-forms-account-property-form .lat-long-row{
        display: none;
    }

    #xmetr-real-estate-forms-account-property-form  .select-location-fields div:nth-child(1),
    #xmetr-real-estate-forms-account-property-form  .select-location-fields div:nth-child(3)
    {
        width: 50%;
        display: block;
    }

    #xmetr-real-estate-forms-account-property-form  .select-location-fields div:nth-child(2),
    #xmetr-real-estate-forms-account-property-form  .select-location-fields div:nth-child(4)
    {
        display: none
    }

    #xmetr-real-estate-forms-account-property-form .rental-period-form-group .form-check-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    }

    #xmetr-real-estate-forms-account-property-form .rental-period-form-group .form-check-group label {
        width: calc(50% - 10px) !important;
        margin-right: 0;
    }
    .required_documents-form-group .fieldset-for-multi-check-list{
        background: none !important;
        border: none;
        padding: 0;
        border-radius: 0;
        margin: 0;
    }
    .required_documents-form-group .control-label {
        display: block;
            font-family: var(--title-font-family);
            font-weight: 700;
            font-size: 15px;
            color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
                margin-bottom: .5rem;
    }
 .required_documents-form-group .multi-check-list-wrapper{
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    max-height: none;
    }
    .required_documents-form-group .multi-check-list-wrapper label{
        width: calc(50% - 10px) !important;
        margin: 0 !important;
    }
    div#seo_wrap {
    display: none;
}
</style>
<div class="body_content bg-[#F7F7F7] flex flex-col items-center gap-[30px] pt-[30px] pb-[40px]">
    <h2 class="title text-center capitalize">
        <?php if(request()->is('account/properties/create')): ?>
            <?php echo e(__('Add listing')); ?>

        <?php elseif(request()->is('account/properties/edit/*')): ?>
           <?php echo e(__('Edit listing')); ?>

        <?php endif; ?>
    </h2>
    <div class="x-propertyType max-w-[480px] w-full rounded-[15px] bg-white p-[20px] h-fit flex flex-col gap-[20px] x-fixedParent_wrapper overflow-hidden" style="box-shadow:0px 5px 15px rgba(0, 0, 0, 0.25);">
    <?php echo \Illuminate\View\Factory::parentPlaceholder('content'); ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('core/base::forms.form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/account/forms/base.blade.php ENDPATH**/ ?>