<?php if($showLabel && $showField): ?>
    <?php if($options['wrapper'] !== false): ?>
        <div <?php echo $options['wrapperAttrs']; ?>>
    <?php endif; ?>
<?php endif; ?>

<?php if($showLabel && $options['label'] !== false && $options['label_show']): ?>
    <?php echo Form::customLabel($name, $options['label'], $options['label_attr']); ?>

<?php endif; ?>

<?php if($showField): ?>
    <div
        class="dropzone needsclick"
        id="single-video-upload"
    >
        <div class="dz-message needsclick">
            <?php echo e(trans('plugins/real-estate::property.form.video_upload_placeholder')); ?><br>
        </div>
    </div>
    <?php echo $__env->make('core/base::forms.partials.help-block', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>

<?php echo $__env->make('core/base::forms.partials.errors', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php if($showLabel && $showField): ?>
    <?php if($options['wrapper'] !== false): ?>
        </div>
    <?php endif; ?>

    <div
        id="video-preview-template"
        style="display: none;"
    >
        <div class="dz-preview dz-file-preview">
            <div class="dz-video">
                <video width="120" height="120" controls>
                    <source data-dz-video />
                    Your browser does not support the video tag.
                </video>
            </div>
            <div class="dz-details">
                <div class="dz-size"><span data-dz-size></span></div>
                <div class="dz-filename"><span data-dz-name></span></div>
            </div>
            <div class="dz-progress"><span
                    class="dz-upload"
                    data-dz-uploadprogress
                ></span></div>
            <div class="dz-error-message"><span data-dz-errormessage></span></div>
            <div class="dz-success-mark">
                <svg
                    width="54px"
                    height="54px"
                    viewBox="0 0 54 54"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:sketch="http://www.bohemiancoding.com/sketch/ns"
                >
                    <title>Check</title>
                    <defs></defs>
                    <g
                        id="Page-1"
                        stroke="none"
                        stroke-width="1"
                        fill="none"
                        fill-rule="evenodd"
                        sketch:type="MSPage"
                    >
                        <path
                            id="Oval-2"
                            d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"
                            stroke-opacity="0.198794158"
                            stroke="#747474"
                            fill-opacity="0.816519475"
                            fill="#FFFFFF"
                            sketch:type="MSShapeGroup"
                        ></path>
                    </g>
                </svg>
            </div>
            <div class="dz-error-mark">
                <svg
                    width="54px"
                    height="54px"
                    viewBox="0 0 54 54"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    xmlns:sketch="http://www.bohemiancoding.com/sketch/ns"
                >
                    <title>error</title>
                    <defs></defs>
                    <g
                        id="Page-1"
                        stroke="none"
                        stroke-width="1"
                        fill="none"
                        fill-rule="evenodd"
                        sketch:type="MSPage"
                    >
                        <g
                            id="Check-+-Oval-2"
                            sketch:type="MSLayerGroup"
                            stroke="#747474"
                            stroke-opacity="0.198794158"
                            fill="#FFFFFF"
                            fill-opacity="0.816519475"
                        >
                            <path
                                id="Oval-2"
                                d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"
                                sketch:type="MSShapeGroup"
                            ></path>
                        </g>
                    </g>
                </svg>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
    <style>
        .dropzone {
            padding: 5px;
            border-radius: 10px;
            border: 1px dashed #5E2DC2;

        }
        .dropzone .dz-preview{
            z-index: 1;
        }
        .dropzone .dz-preview.dz-video-preview {
            margin: 10px;
        }


        .dropzone .dz-preview:not(.dz-processing) .dz-progress {
            display: none;
        }

        .dropzone .dz-message {
            margin: 50px 0;
        }

        .dropzone.dz-clickable * {
            cursor: move;
        }
        .dropzone .dz-remove {
            font-size: 0 !important;
            position: absolute;
            border-radius: 10px;
            background: #fbf0ee;
            left: 10px;
            right: unset;
            height: 40px;
            width: 40px;
            color: #fbf0ee;
            top: 10px;
            z-index: 99;
        }
        .dropzone .dz-remove::before {
            content: '';
            display: inline-block;
            width: 18px; /* Set your SVG's width */
            height: 21px; /* Set your SVG's height */
            background-image: url('data:image/svg+xml;charset=utf-8;base64,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');
            background-repeat: no-repeat;
            background-size: contain;
            position: absolute;
            top: 8px;
            left: 11px;
        }
    </style>
    <script>
        'use strict';
        Dropzone.autoDiscover = false;

        $(document).ready(function() {
            var videoDropzone = new Dropzone('#single-video-upload', {
                previewTemplate: document.querySelector('#video-preview-template').innerHTML,
                parallelUploads: 1,
                thumbnailHeight: 120,
                thumbnailWidth: 120,
                addRemoveLinks: true,
                filesizeBase: 1000,
                uploadMultiple: false,
                chunking: <?php echo e(setting('media_chunk_enabled') == '1' ? 'true' : 'false'); ?>,
                forceChunking: true, // forces chunking when file.size < chunkSize
                parallelChunkUploads: false, // allows chunks to be uploaded in parallel (this is independent of the parallelUploads option)
                chunkSize: <?php echo e(setting('media_chunk_size', config('core.media.media.chunk.chunk_size'))); ?>, // chunk size 1,000,000 bytes (~1MB)
                retryChunks: true, // retry chunks on failure
                retryChunksLimit: 3, // retry maximum of 3 times (default is 3)
                timeout: 0, // MB,
                maxFilesize: <?php echo e(RealEstateHelper::maxVideoFilesizeUploadByAgent()); ?>, // MB
                maxFiles: 1, // only one video file
                paramName: 'file',
                acceptedFiles: 'video/*',
                url: '<?php echo e(route('public.account.upload.video')); ?>',
                sending: function(file, xhr, formData) {
                    formData.append('_token', '<?php echo e(csrf_token()); ?>');
                },
                thumbnail: function(file, dataUrl) {
                    if (file.previewElement) {
                        file.previewElement.classList.remove('dz-file-preview');
                        var videos = file.previewElement.querySelectorAll('[data-dz-video]');
                        for (var i = 0; i < videos.length; i++) {
                            var videoElement = videos[i];
                            videoElement.src = dataUrl;
                        }
                        setTimeout(function() {
                            file.previewElement.classList.add('dz-video-preview');
                        }, 1);

                        if (file.url) {
                            $(file.previewElement).append(
                                '<input type="hidden" name="<?php echo e($name); ?>" value="' + file
                                .url + '" />');
                        }
                    }
                },
                success: function(file, response) {
                    if (response.error) {
                        Xmetr.showError(response.message);
                    } else {
                        if (<?php echo e(setting('media_chunk_enabled') == '1' ? 'true' : 'false'); ?>) {
                            response = JSON.parse(file.xhr.response);
                        }
                    }

                    // Remove any existing hidden inputs first
                    var form = $('#single-video-upload').closest('form');
                    form.find('input[name="<?php echo e($name); ?>"]').remove();
                    form.find('input[name="<?php echo e($name); ?>_thumbnail"]').remove();

                    // Add new hidden inputs
                    $(file.previewElement).append(
                        '<input type="hidden" name="<?php echo e($name); ?>" value="' + response.data
                        .url + '" />');

                    // Store thumbnail URL if available
                    if (response.data.thumbnail_url) {
                        $(file.previewElement).append(
                            '<input type="hidden" name="<?php echo e($name); ?>_thumbnail" value="' + response.data.thumbnail_url + '" />');
                    }

                    console.log('Video uploaded successfully:', response.data.url);
                },
                removedfile: function(file) {
                    if (!confirm('<?php echo e(__('Do you want to delete this video?')); ?>')) {
                        return false;
                    }

                    // Get video URL for server deletion
                    var videoUrl = file.url || $(file.previewElement).find('input[name="<?php echo e($name); ?>"]').val();

                    // If we have a video URL, delete it from server
                    if (videoUrl) {
                        $.ajax({
                            url: '/account/ajax/delete-video',
                            type: 'POST',
                            data: {
                                video_url: videoUrl,
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                if (response.error) {
                                    console.error('Server deletion failed:', response.message);
                                    Xmetr.showError(response.message);
                                } else {
                                    console.log('Video deleted from server successfully');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('AJAX deletion failed:', error);
                                // Continue with frontend removal even if server deletion fails
                            }
                        });
                    }

                    // Remove hidden input fields for video and thumbnail from the preview element
                    $(file.previewElement).find('input[name="<?php echo e($name); ?>"]').remove();
                    $(file.previewElement).find('input[name="<?php echo e($name); ?>_thumbnail"]').remove();

                    // Remove any existing hidden inputs with the same name from the form
                    var form = $('#single-video-upload').closest('form');
                    form.find('input[name="<?php echo e($name); ?>"]').remove();
                    form.find('input[name="<?php echo e($name); ?>_thumbnail"]').remove();

                    // Add a hidden field to indicate video should be cleared
                    form.append('<input type="hidden" name="<?php echo e($name); ?>" value="" />');

                    // Reset dropzone options
                    videoDropzone.options.maxFiles = 1;
                    $('.dz-message.needsclick').show();

                    // Remove the preview element
                    if (file.previewElement != null && file.previewElement.parentNode) {
                        file.previewElement.parentNode.removeChild(file.previewElement);
                    }

                    console.log('Video removed from frontend successfully');
                    return false;
                }
            });

            <?php if($options['value']): ?>
                var file = {
                    name: '<?php echo e(File::name($options['value'])); ?>',
                    size: '<?php echo e(Storage::exists($options['value']) ? Storage::size($options['value']) : 0); ?>',
                    url: '<?php echo e($options['value']); ?>',
                    full_url: '<?php echo e(RvMedia::url($options['value'])); ?>'
                };

                videoDropzone.options.addedfile.call(videoDropzone, file);
                videoDropzone.options.thumbnail.call(videoDropzone, file, file.full_url);

                // Add hidden input for existing video
                $(file.previewElement).append('<input type="hidden" name="<?php echo e($name); ?>" value="<?php echo e($options['value']); ?>" />');

                videoDropzone.options.maxFiles = 0;
                $('.dz-message.needsclick').hide();
            <?php endif; ?>
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\laragon\www\xmetr\platform/plugins/real-estate/resources/views/account/forms/fields/single-video-upload.blade.php ENDPATH**/ ?>